var BlmMonitor=function(){"use strict";function t(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function e(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,s(i.key),i)}}function n(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function i(t,e,n){return(e=s(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function r(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function o(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r(Object(n),!0).forEach((function(e){i(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function s(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}var h="_blmsdk_did",u="_blmsdk_as_uid",c="_blmsdk_traceid",f="_blmsdk_s",l="__lxvalidation",d=window.mainDomain||"yueyuechuxing.cn",p={"cp-admin":"https://admin-daily.".concat(d),common:"https://apitest.".concat(d)},g={"cp-admin":"https://admin.".concat(d),common:"https://api2.".concat(d)},v=function(){window.BlmAnalysis&&(window.console.log('【"blm-analysis日志:】'),window.console.log&&window.console.log.apply(window.console,arguments))},m=Object.prototype.toString,y=function(t){return a(t)};function b(t,e){return y(t)===e}function w(t){return b(t,"string")}function S(t){return b(t,"function")}function T(t){return b(t,"number")}function E(t){return t&&"[object Array]"===m.call(t)}function D(t){return!(!t||1!==t.nodeType)}var x,O,A="blm_monitor_cache",I={get:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:A,e=null;try{var n=localStorage.getItem(t);n&&(e=JSON.parse(n))}catch(t){v(t)}return e},add:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:A,e=arguments.length>1?arguments[1]:void 0;if(e instanceof Array){var n=this.get();e=e.concat(n);try{localStorage.setItem(t,JSON.stringify(e))}catch(t){v(t)}}},clear:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:A;try{localStorage.removeItem(t)}catch(t){v(t)}}},R=["Baiduspider","googlebot","360Spider","haosouspider","YoudaoBot","Sogou News Spider","Yisouspider","Googlebot"],B="",N="|",P=function(t,e,n){t.addEventListener?t.addEventListener(e,n,!1):t.attachEvent&&t.attachEvent("on"+e,n)},V=function(t,e){var n,i={};for(n in t)i[n]=t[n];for(n in e)e.hasOwnProperty(n)&&void 0!==e[n]&&(i[n]=e[n]);return i},M=function(t){var e="";if((t=t||navigator.userAgent).indexOf("Mac OS X")>-1){e=(t.match(/OS [\d._]*/gi)+"").replace(/[^0-9|_.]/gi,"").replace(/_/gi,".")}else e=t.indexOf("Android")>-1||t.indexOf("Linux")>-1?t.substr(t.indexOf("Android")+8,t.indexOf(";",t.indexOf("Android"))-t.indexOf("Android")-8):"unknown";return{isIOS:!!t.match(/iOS|iPad|iPhone/i),isAndroid:!!t.match(/Android/i),isMobile:!!t.match(/iOS|iPad|iPhone|Android|windows Phone/i),isQQ:!!t.match(/qq/i),isWeixin:!!t.match(/micromessenger/i),isWeibo:!!t.match(/weibo/i),isWindows:!!t.match(/windows|win32/i),isMac:!!t.match(/macintosh|mac os x/i),osVersion:e,isInnerYYApp:!!t.match(/YYApp/i)}},C=function(t,e,n,i){var r,o,s;i=i||document.domain;var a=t+"="+encodeURIComponent(e)+";path=/;domain="+i;void 0===n||isNaN(n)||(r=new Date,o=60*parseInt(n,10)*1e3,s=r.getTime()+o,r.setTime(s),a+=";expires="+r.toUTCString());try{document.cookie=a}catch(t){v(t)}},k=function(t,e,n){var i=document.domain,r=i.split("."),o=r.length,s=o-1,a="",h=!1;for(e=""+e;!h&&s>=0&&(i=r.slice(s,o).join("."),C(t,e,n,i),void 0!==(a=j(t))&&(h=a===e),s--,!h););},j=function(t){var e=document.cookie.match(new RegExp("(?:^|;)\\s*"+t+"=([^;]+)"));return decodeURIComponent(e?e[1]:"")},L=(x=window.navigator.userAgent,O=function(){for(var t=1*new Date,e=0;t===1*new Date&&e<200;)e++;return t.toString(16)+e.toString(16)},function(){var t=(screen.height*screen.width).toString(16);return Math.random().toString(16).replace(".","")+"-"+O()+"-"+function(){var t,e,n=[],i=0;function r(t,e){var i,r=0;for(i=0;i<e.length;i++)r|=n[i]<<8*i;return t^r}for(t=0;t<x.length;t++)e=x.charCodeAt(t),n.unshift(255&e),n.length>=4&&(i=r(i,n),n=[]);return n.length>0&&(i=r(i,n)),i.toString(16)}()+"-"+t+"-"+O()}),q=function(){return+new Date},H=function(){return Math.random()},U=function(){return q().toString(16)+"-"+Math.floor(66535*H())+"-"+Math.floor(66535*H())},_=function(){var t=function(t){var e;try{(e=sessionStorage.getItem(t))&&(e=JSON.parse(e))}catch(t){v(t)}return e}(c);if(!t){var e=U();return function(t,e){try{var n=JSON.stringify(e);sessionStorage.setItem(t,n)}catch(t){v(t)}}(c,e),e}return t},K=function(){return U()},F=function(){var t,e={mem:B,cookie:j(h),ls:I.get(h)};for(var n in e)e[n]&&(t=e[n]);for(var i in t||(t=L()),e)if(!e[i])switch(i){case"mem":B=t;break;case"cookie":k(h,t,1576800);break;case"ls":I.add(h,t)}return t},W=function(){return Math.floor(1+65636*H()).toString(16).substring(1)};function G(){var t=[],e=+new Date;return t.push(e.toString(16)),t.push(W()),t.push(W()),t.push(W()),t.join("-")}var z=function(t){var e=j(f);return e||(e=[G(),N,"0"].join(""),C(f,e,30)),T(t)?e.split(N)[t]:e.split(N)},Z=function(){var t=function(){var t=0,e=z(1);return isNaN(e)||(t=parseInt(e)),t<0?0:t}();t=t||0;var e=function(t){var e=[],n=z(null);return e.push(n[0]),e.push(T(t)?t:n[1]),e.join(N)}(++t);return C(f,e,30),t};function J(t){var e,n,i,r,o="";for(e=n=0,i=(t=(t+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,r=0;r<i;r++){var s=t.charCodeAt(r),a=null;s<128?n++:a=s>127&&s<2048?String.fromCharCode(s>>6|192,63&s|128):String.fromCharCode(s>>12|224,s>>6&63|128,63&s|128),null!==a&&(n>e&&(o+=t.substring(e,n)),o+=a,e=n=r+1)}return n>e&&(o+=t.substring(e,t.length)),o}var X=function(t){var e,n,i,r,o,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",a=0,h=0,u="",c=[];if(!t)return t;t=J(t);do{e=(o=t.charCodeAt(a++)<<16|t.charCodeAt(a++)<<8|t.charCodeAt(a++))>>18&63,n=o>>12&63,i=o>>6&63,r=63&o,c[h++]=s.charAt(e)+s.charAt(n)+s.charAt(i)+s.charAt(r)}while(a<t.length);switch(u=c.join(""),t.length%3){case 1:u=u.slice(0,-2)+"==";break;case 2:u=u.slice(0,-1)+"="}return u},Y=function(t){var e=X(t),n=window.btoa,i=navigator.userAgent;try{S(n)&&n(J(i))===X(i)&&(e=n(J(t)))}catch(t){v(t)}return e};S(window.setImmediate)&&/\[native code\]/.test(window.setImmediate.toString())||S(window.Promise)&&/\[native code\]/.test(window.Promise.toString());var Q=function(t,e,n){var i,r;if(t)if(E(t))for(var o=0;o<t.length;o++){if(!1===e.call(n,t[o],o,t))break}else for(var s in t){if(i=t,r=s,Object.prototype.hasOwnProperty.call(i,r))if(!1===e.call(n,t[s],s,t))break}},$=function(t,e){e=e||window;var n=!1,i=!0,r=e.document,o=r.documentElement,s=r.addEventListener,a=s?"addEventListener":"attachEvent",h=s?"removeEventListener":"detachEvent",u=s?"":"on",c=function i(o){"readystatechange"==o.type&&"complete"!=r.readyState||(("load"==o.type?e:r)[h](u+o.type,i,!1),!n&&(n=!0)&&t.call(e,o.type||o))};if("complete"==r.readyState)t.call(e,"lazy");else{if(!s&&o.doScroll){try{i=!e.frameElement}catch(t){logger.log(t)}i&&function t(){try{o.doScroll("left")}catch(e){return void setTimeout(t,50)}c("poll")}()}r[a](u+"DOMContentLoaded",c,!1),r[a](u+"readystatechange",c,!1),e[a](u+"load",c,!1)}},tt=function(){return"/".concat("cp-admin"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"")?"admin":"h5")},et=window.XMLHttpRequest,nt=function(){};!function(){if(et){var t=et.prototype.open,e=et.prototype.send;et.prototype.open=function(e,n){return this.url=n,this._startTime=+new Date,t.apply(this,arguments)},et.prototype.send=function(){var t=this,n="addEventListener",i="onreadystatechange",r=function(e){if(e){var n,i=+new Date-t._startTime;e.duration=i;try{-1!==t.getAllResponseHeaders("content-type").indexOf("application/json")&&(n=e.currentTarget.response,n=JSON.parse(n))}catch(t){v(t)}200===e.currentTarget.status?t.success&&t.success(n):t.fail&&t.fail(n)}};if(n in this)this[n]("load",r),this[n]("error",r),this[n]("abort",r);else{var o=this[i];this[i]=function(t){4===this.readyState&&r(t),o&&o.apply(this,arguments)}}return e.apply(this,arguments)}}}();var it=function(t){if(t){var e,n=window.navigator.userAgent,i=-1!==window.navigator.appName.indexOf("Microsoft Internet Explorer")&&(-1!==n.indexOf("MSIE 8.0")||-1!==n.indexOf("MSIE 9.0"))&&window.XDomainRequest;if((e=i?new XDomainRequest:new XMLHttpRequest).open(t.type||"GET",t.url,!0),e.success=t.success||nt,e.fail=t.fail||nt,"POST"===t.type){if(t.header&&!i)for(var r in t.header)t.header.hasOwnProperty(r)&&e.setRequestHeader(r,t.header[r]);e.send(t.data)}else e.send();return e}},rt=!1;function ot(t){if(t.get().devMode){var e="https://eventlog-test.".concat(window.mainDomain||"yueyuechuxing.cn","/admin/v1/eventlog/tracking/sdk/isExistWhite"),n=t.get(),i=n.category,r=n.appnm,o=n.uid;if(!o||!i||!r)return;if(rt)return;rt=!0;var s={category:i,appnm:r,userId:o,diu:F()};it({type:"POST",url:e,header:{"Content-Type":"application/json"},data:JSON.stringify(s),success:function(e){e.data&&t.updateReportDomain(p)}})}}var st="0123456789abcdefghijklmnopqrstuvwxyz";function at(t){return st.charAt(t)}function ht(t,e){return t&e}function ut(t,e){return t|e}function ct(t,e){return t^e}function ft(t,e){return t&~e}function lt(t){if(0==t)return-1;var e=0;return 65535&t||(t>>=16,e+=16),255&t||(t>>=8,e+=8),15&t||(t>>=4,e+=4),3&t||(t>>=2,e+=2),1&t||++e,e}function dt(t){for(var e=0;0!=t;)t&=t-1,++e;return e}var pt,gt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function vt(t){var e,n,i="";for(e=0;e+3<=t.length;e+=3)n=parseInt(t.substring(e,e+3),16),i+=gt.charAt(n>>6)+gt.charAt(63&n);for(e+1==t.length?(n=parseInt(t.substring(e,e+1),16),i+=gt.charAt(n<<2)):e+2==t.length&&(n=parseInt(t.substring(e,e+2),16),i+=gt.charAt(n>>2)+gt.charAt((3&n)<<4));(3&i.length)>0;)i+="=";return i}function mt(t){var e,n="",i=0,r=0;for(e=0;e<t.length&&"="!=t.charAt(e);++e){var o=gt.indexOf(t.charAt(e));o<0||(0==i?(n+=at(o>>2),r=3&o,i=1):1==i?(n+=at(r<<2|o>>4),r=15&o,i=2):2==i?(n+=at(r),n+=at(o>>2),r=3&o,i=3):(n+=at(r<<2|o>>4),n+=at(15&o),i=0))}return 1==i&&(n+=at(r<<2)),n}var yt,bt=function(t){var e;if(void 0===pt){var n="0123456789ABCDEF",i=" \f\n\r\t \u2028\u2029";for(pt={},e=0;e<16;++e)pt[n.charAt(e)]=e;for(n=n.toLowerCase(),e=10;e<16;++e)pt[n.charAt(e)]=e;for(e=0;e<8;++e)pt[i.charAt(e)]=-1}var r=[],o=0,s=0;for(e=0;e<t.length;++e){var a=t.charAt(e);if("="==a)break;if(-1!=(a=pt[a])){if(void 0===a)throw new Error("Illegal character at offset "+e);o|=a,++s>=2?(r[r.length]=o,o=0,s=0):o<<=4}}if(s)throw new Error("Hex encoding incomplete: 4 bits missing");return r},wt={decode:function(t){var e;if(void 0===yt){var n="= \f\n\r\t \u2028\u2029";for(yt=Object.create(null),e=0;e<64;++e)yt["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(e)]=e;for(yt["-"]=62,yt._=63,e=0;e<9;++e)yt[n.charAt(e)]=-1}var i=[],r=0,o=0;for(e=0;e<t.length;++e){var s=t.charAt(e);if("="==s)break;if(-1!=(s=yt[s])){if(void 0===s)throw new Error("Illegal character at offset "+e);r|=s,++o>=4?(i[i.length]=r>>16,i[i.length]=r>>8&255,i[i.length]=255&r,r=0,o=0):r<<=6}}switch(o){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:i[i.length]=r>>10;break;case 3:i[i.length]=r>>16,i[i.length]=r>>8&255}return i},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(t){var e=wt.re.exec(t);if(e)if(e[1])t=e[1];else{if(!e[2])throw new Error("RegExp out of sync");t=e[2]}return wt.decode(t)}},St=1e13,Tt=function(){function t(t){this.buf=[+t||0]}return t.prototype.mulAdd=function(t,e){var n,i,r=this.buf,o=r.length;for(n=0;n<o;++n)(i=r[n]*t+e)<St?e=0:i-=(e=0|i/St)*St,r[n]=i;e>0&&(r[n]=e)},t.prototype.sub=function(t){var e,n,i=this.buf,r=i.length;for(e=0;e<r;++e)(n=i[e]-t)<0?(n+=St,t=1):t=0,i[e]=n;for(;0===i[i.length-1];)i.pop()},t.prototype.toString=function(t){if(10!=(t||10))throw new Error("only base 10 is supported");for(var e=this.buf,n=e[e.length-1].toString(),i=e.length-2;i>=0;--i)n+=(St+e[i]).toString().substring(1);return n},t.prototype.valueOf=function(){for(var t=this.buf,e=0,n=t.length-1;n>=0;--n)e=e*St+t[n];return e},t.prototype.simplify=function(){var t=this.buf;return 1==t.length?t[0]:this},t}(),Et=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,Dt=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function xt(t,e){return t.length>e&&(t=t.substring(0,e)+"…"),t}var Ot,At=function(){function t(e,n){this.hexDigits="0123456789ABCDEF",e instanceof t?(this.enc=e.enc,this.pos=e.pos):(this.enc=e,this.pos=n)}return t.prototype.get=function(t){if(void 0===t&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset ".concat(t," on a stream of length ").concat(this.enc.length));return"string"==typeof this.enc?this.enc.charCodeAt(t):this.enc[t]},t.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(15&t)},t.prototype.hexDump=function(t,e,n){for(var i="",r=t;r<e;++r)if(i+=this.hexByte(this.get(r)),!0!==n)switch(15&r){case 7:i+="  ";break;case 15:i+="\n";break;default:i+=" "}return i},t.prototype.isASCII=function(t,e){for(var n=t;n<e;++n){var i=this.get(n);if(i<32||i>176)return!1}return!0},t.prototype.parseStringISO=function(t,e){for(var n="",i=t;i<e;++i)n+=String.fromCharCode(this.get(i));return n},t.prototype.parseStringUTF=function(t,e){for(var n="",i=t;i<e;){var r=this.get(i++);n+=r<128?String.fromCharCode(r):r>191&&r<224?String.fromCharCode((31&r)<<6|63&this.get(i++)):String.fromCharCode((15&r)<<12|(63&this.get(i++))<<6|63&this.get(i++))}return n},t.prototype.parseStringBMP=function(t,e){for(var n,i,r="",o=t;o<e;)n=this.get(o++),i=this.get(o++),r+=String.fromCharCode(n<<8|i);return r},t.prototype.parseTime=function(t,e,n){var i=this.parseStringISO(t,e),r=(n?Et:Dt).exec(i);return r?(n&&(r[1]=+r[1],r[1]+=+r[1]<70?2e3:1900),i=r[1]+"-"+r[2]+"-"+r[3]+" "+r[4],r[5]&&(i+=":"+r[5],r[6]&&(i+=":"+r[6],r[7]&&(i+="."+r[7]))),r[8]&&(i+=" UTC","Z"!=r[8]&&(i+=r[8],r[9]&&(i+=":"+r[9]))),i):"Unrecognized time: "+i},t.prototype.parseInteger=function(t,e){for(var n,i=this.get(t),r=i>127,o=r?255:0,s="";i==o&&++t<e;)i=this.get(t);if(0===(n=e-t))return r?-1:0;if(n>4){for(s=i,n<<=3;!(128&(+s^o));)s=+s<<1,--n;s="("+n+" bit)\n"}r&&(i-=256);for(var a=new Tt(i),h=t+1;h<e;++h)a.mulAdd(256,this.get(h));return s+a.toString()},t.prototype.parseBitString=function(t,e,n){for(var i=this.get(t),r="("+((e-t-1<<3)-i)+" bit)\n",o="",s=t+1;s<e;++s){for(var a=this.get(s),h=s==e-1?i:0,u=7;u>=h;--u)o+=a>>u&1?"1":"0";if(o.length>n)return r+xt(o,n)}return r+o},t.prototype.parseOctetString=function(t,e,n){if(this.isASCII(t,e))return xt(this.parseStringISO(t,e),n);var i=e-t,r="("+i+" byte)\n";i>(n/=2)&&(e=t+n);for(var o=t;o<e;++o)r+=this.hexByte(this.get(o));return i>n&&(r+="…"),r},t.prototype.parseOID=function(t,e,n){for(var i="",r=new Tt,o=0,s=t;s<e;++s){var a=this.get(s);if(r.mulAdd(128,127&a),o+=7,!(128&a)){if(""===i)if((r=r.simplify())instanceof Tt)r.sub(80),i="2."+r.toString();else{var h=r<80?r<40?0:1:2;i=h+"."+(r-40*h)}else i+="."+r.toString();if(i.length>n)return xt(i,n);r=new Tt,o=0}}return o>0&&(i+=".incomplete"),i},t}(),It=function(){function t(t,e,n,i,r){if(!(i instanceof Rt))throw new Error("Invalid tag value.");this.stream=t,this.header=e,this.length=n,this.tag=i,this.sub=r}return t.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},t.prototype.content=function(t){if(void 0===this.tag)return null;void 0===t&&(t=1/0);var e=this.posContent(),n=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+n,t);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(e)?"false":"true";case 2:return this.stream.parseInteger(e,e+n);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(e,e+n,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+n,t);case 6:return this.stream.parseOID(e,e+n,t);case 16:case 17:return null!==this.sub?"("+this.sub.length+" elem)":"(no elem)";case 12:return xt(this.stream.parseStringUTF(e,e+n),t);case 18:case 19:case 20:case 21:case 22:case 26:return xt(this.stream.parseStringISO(e,e+n),t);case 30:return xt(this.stream.parseStringBMP(e,e+n),t);case 23:case 24:return this.stream.parseTime(e,e+n,23==this.tag.tagNumber)}return null},t.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(null===this.sub?"null":this.sub.length)+"]"},t.prototype.toPrettyString=function(t){void 0===t&&(t="");var e=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(e+="+"),e+=this.length,this.tag.tagConstructed?e+=" (constructed)":!this.tag.isUniversal()||3!=this.tag.tagNumber&&4!=this.tag.tagNumber||null===this.sub||(e+=" (encapsulates)"),e+="\n",null!==this.sub){t+="  ";for(var n=0,i=this.sub.length;n<i;++n)e+=this.sub[n].toPrettyString(t)}return e},t.prototype.posStart=function(){return this.stream.pos},t.prototype.posContent=function(){return this.stream.pos+this.header},t.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},t.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},t.decodeLength=function(t){var e=t.get(),n=127&e;if(n==e)return n;if(n>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(0===n)return null;e=0;for(var i=0;i<n;++i)e=256*e+t.get();return e},t.prototype.getHexStringValue=function(){var t=this.toHexString(),e=2*this.header,n=2*this.length;return t.substr(e,n)},t.decode=function(e){var n;n=e instanceof At?e:new At(e,0);var i=new At(n),r=new Rt(n),o=t.decodeLength(n),s=n.pos,a=s-i.pos,h=null,u=function(){var e=[];if(null!==o){for(var i=s+o;n.pos<i;)e[e.length]=t.decode(n);if(n.pos!=i)throw new Error("Content size is not correct for container starting at offset "+s)}else try{for(;;){var r=t.decode(n);if(r.tag.isEOC())break;e[e.length]=r}o=s-n.pos}catch(t){throw new Error("Exception while decoding undefined length content: "+t)}return e};if(r.tagConstructed)h=u();else if(r.isUniversal()&&(3==r.tagNumber||4==r.tagNumber))try{if(3==r.tagNumber&&0!=n.get())throw new Error("BIT STRINGs with unused bits cannot encapsulate.");h=u();for(var c=0;c<h.length;++c)if(h[c].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch(t){h=null}if(null===h){if(null===o)throw new Error("We can't skip over an invalid tag with undefined length at offset "+s);n.pos=s+Math.abs(o)}return new t(i,a,o,r,h)},t}(),Rt=function(){function t(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=!!(32&e),this.tagNumber=31&e,31==this.tagNumber){var n=new Tt;do{e=t.get(),n.mulAdd(128,127&e)}while(128&e);this.tagNumber=n.simplify()}}return t.prototype.isUniversal=function(){return 0===this.tagClass},t.prototype.isEOC=function(){return 0===this.tagClass&&0===this.tagNumber},t}(),Bt=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],Nt=(1<<26)/Bt[Bt.length-1],Pt=function(){function t(t,e,n){null!=t&&("number"==typeof t?this.fromNumber(t,e,n):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}return t.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var n,i=(1<<e)-1,r=!1,o="",s=this.t,a=this.DB-s*this.DB%e;if(s-- >0)for(a<this.DB&&(n=this[s]>>a)>0&&(r=!0,o=at(n));s>=0;)a<e?(n=(this[s]&(1<<a)-1)<<e-a,n|=this[--s]>>(a+=this.DB-e)):(n=this[s]>>(a-=e)&i,a<=0&&(a+=this.DB,--s)),n>0&&(r=!0),r&&(o+=at(n));return r?o:"0"},t.prototype.negate=function(){var e=jt();return t.ZERO.subTo(this,e),e},t.prototype.abs=function(){return this.s<0?this.negate():this},t.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var n=this.t;if(0!=(e=n-t.t))return this.s<0?-e:e;for(;--n>=0;)if(0!=(e=this[n]-t[n]))return e;return 0},t.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+Wt(this[this.t-1]^this.s&this.DM)},t.prototype.mod=function(e){var n=jt();return this.abs().divRemTo(e,null,n),this.s<0&&n.compareTo(t.ZERO)>0&&e.subTo(n,n),n},t.prototype.modPowInt=function(t,e){var n;return n=t<256||e.isEven()?new Mt(e):new Ct(e),this.exp(t,n)},t.prototype.clone=function(){var t=jt();return this.copyTo(t),t},t.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},t.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},t.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},t.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},t.prototype.toByteArray=function(){var t=this.t,e=[];e[0]=this.s;var n,i=this.DB-t*this.DB%8,r=0;if(t-- >0)for(i<this.DB&&(n=this[t]>>i)!=(this.s&this.DM)>>i&&(e[r++]=n|this.s<<this.DB-i);t>=0;)i<8?(n=(this[t]&(1<<i)-1)<<8-i,n|=this[--t]>>(i+=this.DB-8)):(n=this[t]>>(i-=8)&255,i<=0&&(i+=this.DB,--t)),128&n&&(n|=-256),0==r&&(128&this.s)!=(128&n)&&++r,(r>0||n!=this.s)&&(e[r++]=n);return e},t.prototype.equals=function(t){return 0==this.compareTo(t)},t.prototype.min=function(t){return this.compareTo(t)<0?this:t},t.prototype.max=function(t){return this.compareTo(t)>0?this:t},t.prototype.and=function(t){var e=jt();return this.bitwiseTo(t,ht,e),e},t.prototype.or=function(t){var e=jt();return this.bitwiseTo(t,ut,e),e},t.prototype.xor=function(t){var e=jt();return this.bitwiseTo(t,ct,e),e},t.prototype.andNot=function(t){var e=jt();return this.bitwiseTo(t,ft,e),e},t.prototype.not=function(){for(var t=jt(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},t.prototype.shiftLeft=function(t){var e=jt();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},t.prototype.shiftRight=function(t){var e=jt();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},t.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+lt(this[t]);return this.s<0?this.t*this.DB:-1},t.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,n=0;n<this.t;++n)t+=dt(this[n]^e);return t},t.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:!!(this[e]&1<<t%this.DB)},t.prototype.setBit=function(t){return this.changeBit(t,ut)},t.prototype.clearBit=function(t){return this.changeBit(t,ft)},t.prototype.flipBit=function(t){return this.changeBit(t,ct)},t.prototype.add=function(t){var e=jt();return this.addTo(t,e),e},t.prototype.subtract=function(t){var e=jt();return this.subTo(t,e),e},t.prototype.multiply=function(t){var e=jt();return this.multiplyTo(t,e),e},t.prototype.divide=function(t){var e=jt();return this.divRemTo(t,e,null),e},t.prototype.remainder=function(t){var e=jt();return this.divRemTo(t,null,e),e},t.prototype.divideAndRemainder=function(t){var e=jt(),n=jt();return this.divRemTo(t,e,n),[e,n]},t.prototype.modPow=function(t,e){var n,i,r=t.bitLength(),o=Ft(1);if(r<=0)return o;n=r<18?1:r<48?3:r<144?4:r<768?5:6,i=r<8?new Mt(e):e.isEven()?new kt(e):new Ct(e);var s=[],a=3,h=n-1,u=(1<<n)-1;if(s[1]=i.convert(this),n>1){var c=jt();for(i.sqrTo(s[1],c);a<=u;)s[a]=jt(),i.mulTo(c,s[a-2],s[a]),a+=2}var f,l,d=t.t-1,p=!0,g=jt();for(r=Wt(t[d])-1;d>=0;){for(r>=h?f=t[d]>>r-h&u:(f=(t[d]&(1<<r+1)-1)<<h-r,d>0&&(f|=t[d-1]>>this.DB+r-h)),a=n;!(1&f);)f>>=1,--a;if((r-=a)<0&&(r+=this.DB,--d),p)s[f].copyTo(o),p=!1;else{for(;a>1;)i.sqrTo(o,g),i.sqrTo(g,o),a-=2;a>0?i.sqrTo(o,g):(l=o,o=g,g=l),i.mulTo(g,s[f],o)}for(;d>=0&&!(t[d]&1<<r);)i.sqrTo(o,g),l=o,o=g,g=l,--r<0&&(r=this.DB-1,--d)}return i.revert(o)},t.prototype.modInverse=function(e){var n=e.isEven();if(this.isEven()&&n||0==e.signum())return t.ZERO;for(var i=e.clone(),r=this.clone(),o=Ft(1),s=Ft(0),a=Ft(0),h=Ft(1);0!=i.signum();){for(;i.isEven();)i.rShiftTo(1,i),n?(o.isEven()&&s.isEven()||(o.addTo(this,o),s.subTo(e,s)),o.rShiftTo(1,o)):s.isEven()||s.subTo(e,s),s.rShiftTo(1,s);for(;r.isEven();)r.rShiftTo(1,r),n?(a.isEven()&&h.isEven()||(a.addTo(this,a),h.subTo(e,h)),a.rShiftTo(1,a)):h.isEven()||h.subTo(e,h),h.rShiftTo(1,h);i.compareTo(r)>=0?(i.subTo(r,i),n&&o.subTo(a,o),s.subTo(h,s)):(r.subTo(i,r),n&&a.subTo(o,a),h.subTo(s,h))}return 0!=r.compareTo(t.ONE)?t.ZERO:h.compareTo(e)>=0?h.subtract(e):h.signum()<0?(h.addTo(e,h),h.signum()<0?h.add(e):h):h},t.prototype.pow=function(t){return this.exp(t,new Vt)},t.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),n=t.s<0?t.negate():t.clone();if(e.compareTo(n)<0){var i=e;e=n,n=i}var r=e.getLowestSetBit(),o=n.getLowestSetBit();if(o<0)return e;for(r<o&&(o=r),o>0&&(e.rShiftTo(o,e),n.rShiftTo(o,n));e.signum()>0;)(r=e.getLowestSetBit())>0&&e.rShiftTo(r,e),(r=n.getLowestSetBit())>0&&n.rShiftTo(r,n),e.compareTo(n)>=0?(e.subTo(n,e),e.rShiftTo(1,e)):(n.subTo(e,n),n.rShiftTo(1,n));return o>0&&n.lShiftTo(o,n),n},t.prototype.isProbablePrime=function(t){var e,n=this.abs();if(1==n.t&&n[0]<=Bt[Bt.length-1]){for(e=0;e<Bt.length;++e)if(n[0]==Bt[e])return!0;return!1}if(n.isEven())return!1;for(e=1;e<Bt.length;){for(var i=Bt[e],r=e+1;r<Bt.length&&i<Nt;)i*=Bt[r++];for(i=n.modInt(i);e<r;)if(i%Bt[e++]==0)return!1}return n.millerRabin(t)},t.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},t.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},t.prototype.fromString=function(e,n){var i;if(16==n)i=4;else if(8==n)i=3;else if(256==n)i=8;else if(2==n)i=1;else if(32==n)i=5;else{if(4!=n)return void this.fromRadix(e,n);i=2}this.t=0,this.s=0;for(var r=e.length,o=!1,s=0;--r>=0;){var a=8==i?255&+e[r]:Kt(e,r);a<0?"-"==e.charAt(r)&&(o=!0):(o=!1,0==s?this[this.t++]=a:s+i>this.DB?(this[this.t-1]|=(a&(1<<this.DB-s)-1)<<s,this[this.t++]=a>>this.DB-s):this[this.t-1]|=a<<s,(s+=i)>=this.DB&&(s-=this.DB))}8==i&&128&+e[0]&&(this.s=-1,s>0&&(this[this.t-1]|=(1<<this.DB-s)-1<<s)),this.clamp(),o&&t.ZERO.subTo(this,this)},t.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},t.prototype.dlShiftTo=function(t,e){var n;for(n=this.t-1;n>=0;--n)e[n+t]=this[n];for(n=t-1;n>=0;--n)e[n]=0;e.t=this.t+t,e.s=this.s},t.prototype.drShiftTo=function(t,e){for(var n=t;n<this.t;++n)e[n-t]=this[n];e.t=Math.max(this.t-t,0),e.s=this.s},t.prototype.lShiftTo=function(t,e){for(var n=t%this.DB,i=this.DB-n,r=(1<<i)-1,o=Math.floor(t/this.DB),s=this.s<<n&this.DM,a=this.t-1;a>=0;--a)e[a+o+1]=this[a]>>i|s,s=(this[a]&r)<<n;for(a=o-1;a>=0;--a)e[a]=0;e[o]=s,e.t=this.t+o+1,e.s=this.s,e.clamp()},t.prototype.rShiftTo=function(t,e){e.s=this.s;var n=Math.floor(t/this.DB);if(n>=this.t)e.t=0;else{var i=t%this.DB,r=this.DB-i,o=(1<<i)-1;e[0]=this[n]>>i;for(var s=n+1;s<this.t;++s)e[s-n-1]|=(this[s]&o)<<r,e[s-n]=this[s]>>i;i>0&&(e[this.t-n-1]|=(this.s&o)<<r),e.t=this.t-n,e.clamp()}},t.prototype.subTo=function(t,e){for(var n=0,i=0,r=Math.min(t.t,this.t);n<r;)i+=this[n]-t[n],e[n++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i-=t.s;n<this.t;)i+=this[n],e[n++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;n<t.t;)i-=t[n],e[n++]=i&this.DM,i>>=this.DB;i-=t.s}e.s=i<0?-1:0,i<-1?e[n++]=this.DV+i:i>0&&(e[n++]=i),e.t=n,e.clamp()},t.prototype.multiplyTo=function(e,n){var i=this.abs(),r=e.abs(),o=i.t;for(n.t=o+r.t;--o>=0;)n[o]=0;for(o=0;o<r.t;++o)n[o+i.t]=i.am(0,r[o],n,o,0,i.t);n.s=0,n.clamp(),this.s!=e.s&&t.ZERO.subTo(n,n)},t.prototype.squareTo=function(t){for(var e=this.abs(),n=t.t=2*e.t;--n>=0;)t[n]=0;for(n=0;n<e.t-1;++n){var i=e.am(n,e[n],t,2*n,0,1);(t[n+e.t]+=e.am(n+1,2*e[n],t,2*n+1,i,e.t-n-1))>=e.DV&&(t[n+e.t]-=e.DV,t[n+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(n,e[n],t,2*n,0,1)),t.s=0,t.clamp()},t.prototype.divRemTo=function(e,n,i){var r=e.abs();if(!(r.t<=0)){var o=this.abs();if(o.t<r.t)return null!=n&&n.fromInt(0),void(null!=i&&this.copyTo(i));null==i&&(i=jt());var s=jt(),a=this.s,h=e.s,u=this.DB-Wt(r[r.t-1]);u>0?(r.lShiftTo(u,s),o.lShiftTo(u,i)):(r.copyTo(s),o.copyTo(i));var c=s.t,f=s[c-1];if(0!=f){var l=f*(1<<this.F1)+(c>1?s[c-2]>>this.F2:0),d=this.FV/l,p=(1<<this.F1)/l,g=1<<this.F2,v=i.t,m=v-c,y=null==n?jt():n;for(s.dlShiftTo(m,y),i.compareTo(y)>=0&&(i[i.t++]=1,i.subTo(y,i)),t.ONE.dlShiftTo(c,y),y.subTo(s,s);s.t<c;)s[s.t++]=0;for(;--m>=0;){var b=i[--v]==f?this.DM:Math.floor(i[v]*d+(i[v-1]+g)*p);if((i[v]+=s.am(0,b,i,m,0,c))<b)for(s.dlShiftTo(m,y),i.subTo(y,i);i[v]<--b;)i.subTo(y,i)}null!=n&&(i.drShiftTo(c,n),a!=h&&t.ZERO.subTo(n,n)),i.t=c,i.clamp(),u>0&&i.rShiftTo(u,i),a<0&&t.ZERO.subTo(i,i)}}},t.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(!(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},t.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},t.prototype.exp=function(e,n){if(e>4294967295||e<1)return t.ONE;var i=jt(),r=jt(),o=n.convert(this),s=Wt(e)-1;for(o.copyTo(i);--s>=0;)if(n.sqrTo(i,r),(e&1<<s)>0)n.mulTo(r,o,i);else{var a=i;i=r,r=a}return n.revert(i)},t.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},t.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),n=Math.pow(t,e),i=Ft(n),r=jt(),o=jt(),s="";for(this.divRemTo(i,r,o);r.signum()>0;)s=(n+o.intValue()).toString(t).substr(1)+s,r.divRemTo(i,r,o);return o.intValue().toString(t)+s},t.prototype.fromRadix=function(e,n){this.fromInt(0),null==n&&(n=10);for(var i=this.chunkSize(n),r=Math.pow(n,i),o=!1,s=0,a=0,h=0;h<e.length;++h){var u=Kt(e,h);u<0?"-"==e.charAt(h)&&0==this.signum()&&(o=!0):(a=n*a+u,++s>=i&&(this.dMultiply(r),this.dAddOffset(a,0),s=0,a=0))}s>0&&(this.dMultiply(Math.pow(n,s)),this.dAddOffset(a,0)),o&&t.ZERO.subTo(this,this)},t.prototype.fromNumber=function(e,n,i){if("number"==typeof n)if(e<2)this.fromInt(1);else for(this.fromNumber(e,i),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),ut,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(n);)this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(t.ONE.shiftLeft(e-1),this);else{var r=[],o=7&e;r.length=1+(e>>3),n.nextBytes(r),o>0?r[0]&=(1<<o)-1:r[0]=0,this.fromString(r,256)}},t.prototype.bitwiseTo=function(t,e,n){var i,r,o=Math.min(t.t,this.t);for(i=0;i<o;++i)n[i]=e(this[i],t[i]);if(t.t<this.t){for(r=t.s&this.DM,i=o;i<this.t;++i)n[i]=e(this[i],r);n.t=this.t}else{for(r=this.s&this.DM,i=o;i<t.t;++i)n[i]=e(r,t[i]);n.t=t.t}n.s=e(this.s,t.s),n.clamp()},t.prototype.changeBit=function(e,n){var i=t.ONE.shiftLeft(e);return this.bitwiseTo(i,n,i),i},t.prototype.addTo=function(t,e){for(var n=0,i=0,r=Math.min(t.t,this.t);n<r;)i+=this[n]+t[n],e[n++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i+=t.s;n<this.t;)i+=this[n],e[n++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;n<t.t;)i+=t[n],e[n++]=i&this.DM,i>>=this.DB;i+=t.s}e.s=i<0?-1:0,i>0?e[n++]=i:i<-1&&(e[n++]=this.DV+i),e.t=n,e.clamp()},t.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},t.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},t.prototype.multiplyLowerTo=function(t,e,n){var i=Math.min(this.t+t.t,e);for(n.s=0,n.t=i;i>0;)n[--i]=0;for(var r=n.t-this.t;i<r;++i)n[i+this.t]=this.am(0,t[i],n,i,0,this.t);for(r=Math.min(t.t,e);i<r;++i)this.am(0,t[i],n,i,0,e-i);n.clamp()},t.prototype.multiplyUpperTo=function(t,e,n){--e;var i=n.t=this.t+t.t-e;for(n.s=0;--i>=0;)n[i]=0;for(i=Math.max(e-this.t,0);i<t.t;++i)n[this.t+i-e]=this.am(e-i,t[i],n,0,0,this.t+i-e);n.clamp(),n.drShiftTo(1,n)},t.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,n=this.s<0?t-1:0;if(this.t>0)if(0==e)n=this[0]%t;else for(var i=this.t-1;i>=0;--i)n=(e*n+this[i])%t;return n},t.prototype.millerRabin=function(e){var n=this.subtract(t.ONE),i=n.getLowestSetBit();if(i<=0)return!1;var r=n.shiftRight(i);(e=e+1>>1)>Bt.length&&(e=Bt.length);for(var o=jt(),s=0;s<e;++s){o.fromInt(Bt[Math.floor(Math.random()*Bt.length)]);var a=o.modPow(r,this);if(0!=a.compareTo(t.ONE)&&0!=a.compareTo(n)){for(var h=1;h++<i&&0!=a.compareTo(n);)if(0==(a=a.modPowInt(2,this)).compareTo(t.ONE))return!1;if(0!=a.compareTo(n))return!1}}return!0},t.prototype.square=function(){var t=jt();return this.squareTo(t),t},t.prototype.gcda=function(t,e){var n=this.s<0?this.negate():this.clone(),i=t.s<0?t.negate():t.clone();if(n.compareTo(i)<0){var r=n;n=i,i=r}var o=n.getLowestSetBit(),s=i.getLowestSetBit();if(s<0)e(n);else{o<s&&(s=o),s>0&&(n.rShiftTo(s,n),i.rShiftTo(s,i));setTimeout((function t(){(o=n.getLowestSetBit())>0&&n.rShiftTo(o,n),(o=i.getLowestSetBit())>0&&i.rShiftTo(o,i),n.compareTo(i)>=0?(n.subTo(i,n),n.rShiftTo(1,n)):(i.subTo(n,i),i.rShiftTo(1,i)),n.signum()>0?setTimeout(t,0):(s>0&&i.lShiftTo(s,i),setTimeout((function(){e(i)}),0))}),10)}},t.prototype.fromNumberAsync=function(e,n,i,r){if("number"==typeof n)if(e<2)this.fromInt(1);else{this.fromNumber(e,i),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),ut,this),this.isEven()&&this.dAddOffset(1,0);var o=this;setTimeout((function i(){o.dAddOffset(2,0),o.bitLength()>e&&o.subTo(t.ONE.shiftLeft(e-1),o),o.isProbablePrime(n)?setTimeout((function(){r()}),0):setTimeout(i,0)}),0)}else{var s=[],a=7&e;s.length=1+(e>>3),n.nextBytes(s),a>0?s[0]&=(1<<a)-1:s[0]=0,this.fromString(s,256)}},t}(),Vt=function(){function t(){}return t.prototype.convert=function(t){return t},t.prototype.revert=function(t){return t},t.prototype.mulTo=function(t,e,n){t.multiplyTo(e,n)},t.prototype.sqrTo=function(t,e){t.squareTo(e)},t}(),Mt=function(){function t(t){this.m=t}return t.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},t.prototype.mulTo=function(t,e,n){t.multiplyTo(e,n),this.reduce(n)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),Ct=function(){function t(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return t.prototype.convert=function(t){var e=jt();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(Pt.ZERO)>0&&this.m.subTo(e,e),e},t.prototype.revert=function(t){var e=jt();return t.copyTo(e),this.reduce(e),e},t.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var n=32767&t[e],i=n*this.mpl+((n*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[n=e+this.m.t]+=this.m.am(0,i,t,e,0,this.m.t);t[n]>=t.DV;)t[n]-=t.DV,t[++n]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,n){t.multiplyTo(e,n),this.reduce(n)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),kt=function(){function t(t){this.m=t,this.r2=jt(),this.q3=jt(),Pt.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return t.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=jt();return t.copyTo(e),this.reduce(e),e},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,n){t.multiplyTo(e,n),this.reduce(n)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}();function jt(){return new Pt(null)}function Lt(t,e){return new Pt(t,e)}var qt="undefined"!=typeof navigator;qt&&"Microsoft Internet Explorer"==navigator.appName?(Pt.prototype.am=function(t,e,n,i,r,o){for(var s=32767&e,a=e>>15;--o>=0;){var h=32767&this[t],u=this[t++]>>15,c=a*h+u*s;r=((h=s*h+((32767&c)<<15)+n[i]+(1073741823&r))>>>30)+(c>>>15)+a*u+(r>>>30),n[i++]=1073741823&h}return r},Ot=30):qt&&"Netscape"!=navigator.appName?(Pt.prototype.am=function(t,e,n,i,r,o){for(;--o>=0;){var s=e*this[t++]+n[i]+r;r=Math.floor(s/67108864),n[i++]=67108863&s}return r},Ot=26):(Pt.prototype.am=function(t,e,n,i,r,o){for(var s=16383&e,a=e>>14;--o>=0;){var h=16383&this[t],u=this[t++]>>14,c=a*h+u*s;r=((h=s*h+((16383&c)<<14)+n[i]+r)>>28)+(c>>14)+a*u,n[i++]=268435455&h}return r},Ot=28),Pt.prototype.DB=Ot,Pt.prototype.DM=(1<<Ot)-1,Pt.prototype.DV=1<<Ot;Pt.prototype.FV=Math.pow(2,52),Pt.prototype.F1=52-Ot,Pt.prototype.F2=2*Ot-52;var Ht,Ut,_t=[];for(Ht="0".charCodeAt(0),Ut=0;Ut<=9;++Ut)_t[Ht++]=Ut;for(Ht="a".charCodeAt(0),Ut=10;Ut<36;++Ut)_t[Ht++]=Ut;for(Ht="A".charCodeAt(0),Ut=10;Ut<36;++Ut)_t[Ht++]=Ut;function Kt(t,e){var n=_t[t.charCodeAt(e)];return null==n?-1:n}function Ft(t){var e=jt();return e.fromInt(t),e}function Wt(t){var e,n=1;return 0!=(e=t>>>16)&&(t=e,n+=16),0!=(e=t>>8)&&(t=e,n+=8),0!=(e=t>>4)&&(t=e,n+=4),0!=(e=t>>2)&&(t=e,n+=2),0!=(e=t>>1)&&(t=e,n+=1),n}Pt.ZERO=Ft(0),Pt.ONE=Ft(1);var Gt=function(){function t(){this.i=0,this.j=0,this.S=[]}return t.prototype.init=function(t){var e,n,i;for(e=0;e<256;++e)this.S[e]=e;for(n=0,e=0;e<256;++e)n=n+this.S[e]+t[e%t.length]&255,i=this.S[e],this.S[e]=this.S[n],this.S[n]=i;this.i=0,this.j=0},t.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},t}();var zt,Zt,Jt=null;if(null==Jt){Jt=[],Zt=0;var Xt=void 0;if("undefined"!=typeof window&&window.crypto&&window.crypto.getRandomValues){var Yt=new Uint32Array(256);for(window.crypto.getRandomValues(Yt),Xt=0;Xt<Yt.length;++Xt)Jt[Zt++]=255&Yt[Xt]}var Qt=0,$t=function t(e){if((Qt=Qt||0)>=256||Zt>=256)window.removeEventListener?window.removeEventListener("mousemove",t,!1):window.detachEvent&&window.detachEvent("onmousemove",t);else try{var n=e.x+e.y;Jt[Zt++]=255&n,Qt+=1}catch(t){}};"undefined"!=typeof window&&(window.addEventListener?window.addEventListener("mousemove",$t,!1):window.attachEvent&&window.attachEvent("onmousemove",$t))}function te(){if(null==zt){for(zt=new Gt;Zt<256;){var t=Math.floor(65536*Math.random());Jt[Zt++]=255&t}for(zt.init(Jt),Zt=0;Zt<Jt.length;++Zt)Jt[Zt]=0;Zt=0}return zt.next()}var ee=function(){function t(){}return t.prototype.nextBytes=function(t){for(var e=0;e<t.length;++e)t[e]=te()},t}();var ne=function(){function t(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return t.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},t.prototype.doPrivate=function(t){if(null==this.p||null==this.q)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),n=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(n)<0;)e=e.add(this.p);return e.subtract(n).multiply(this.coeff).mod(this.p).multiply(this.q).add(n)},t.prototype.setPublic=function(t,e){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=Lt(t,16),this.e=parseInt(e,16)):console.error("Invalid RSA public key")},t.prototype.encrypt=function(t){var e=this.n.bitLength()+7>>3,n=function(t,e){if(e<t.length+11)return console.error("Message too long for RSA"),null;for(var n=[],i=t.length-1;i>=0&&e>0;){var r=t.charCodeAt(i--);r<128?n[--e]=r:r>127&&r<2048?(n[--e]=63&r|128,n[--e]=r>>6|192):(n[--e]=63&r|128,n[--e]=r>>6&63|128,n[--e]=r>>12|224)}n[--e]=0;for(var o=new ee,s=[];e>2;){for(s[0]=0;0==s[0];)o.nextBytes(s);n[--e]=s[0]}return n[--e]=2,n[--e]=0,new Pt(n)}(t,e);if(null==n)return null;var i=this.doPublic(n);if(null==i)return null;for(var r=i.toString(16),o=r.length,s=0;s<2*e-o;s++)r="0"+r;return r},t.prototype.setPrivate=function(t,e,n){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=Lt(t,16),this.e=parseInt(e,16),this.d=Lt(n,16)):console.error("Invalid RSA private key")},t.prototype.setPrivateEx=function(t,e,n,i,r,o,s,a){null!=t&&null!=e&&t.length>0&&e.length>0?(this.n=Lt(t,16),this.e=parseInt(e,16),this.d=Lt(n,16),this.p=Lt(i,16),this.q=Lt(r,16),this.dmp1=Lt(o,16),this.dmq1=Lt(s,16),this.coeff=Lt(a,16)):console.error("Invalid RSA private key")},t.prototype.generate=function(t,e){var n=new ee,i=t>>1;this.e=parseInt(e,16);for(var r=new Pt(e,16);;){for(;this.p=new Pt(t-i,1,n),0!=this.p.subtract(Pt.ONE).gcd(r).compareTo(Pt.ONE)||!this.p.isProbablePrime(10););for(;this.q=new Pt(i,1,n),0!=this.q.subtract(Pt.ONE).gcd(r).compareTo(Pt.ONE)||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var o=this.p;this.p=this.q,this.q=o}var s=this.p.subtract(Pt.ONE),a=this.q.subtract(Pt.ONE),h=s.multiply(a);if(0==h.gcd(r).compareTo(Pt.ONE)){this.n=this.p.multiply(this.q),this.d=r.modInverse(h),this.dmp1=this.d.mod(s),this.dmq1=this.d.mod(a),this.coeff=this.q.modInverse(this.p);break}}},t.prototype.decrypt=function(t){var e=Lt(t,16),n=this.doPrivate(e);return null==n?null:function(t,e){var n=t.toByteArray(),i=0;for(;i<n.length&&0==n[i];)++i;if(n.length-i!=e-1||2!=n[i])return null;++i;for(;0!=n[i];)if(++i>=n.length)return null;var r="";for(;++i<n.length;){var o=255&n[i];o<128?r+=String.fromCharCode(o):o>191&&o<224?(r+=String.fromCharCode((31&o)<<6|63&n[i+1]),++i):(r+=String.fromCharCode((15&o)<<12|(63&n[i+1])<<6|63&n[i+2]),i+=2)}return r}(n,this.n.bitLength()+7>>3)},t.prototype.generateAsync=function(t,e,n){var i=new ee,r=t>>1;this.e=parseInt(e,16);var o=new Pt(e,16),s=this;setTimeout((function e(){var a=function(){if(s.p.compareTo(s.q)<=0){var t=s.p;s.p=s.q,s.q=t}var i=s.p.subtract(Pt.ONE),r=s.q.subtract(Pt.ONE),a=i.multiply(r);0==a.gcd(o).compareTo(Pt.ONE)?(s.n=s.p.multiply(s.q),s.d=o.modInverse(a),s.dmp1=s.d.mod(i),s.dmq1=s.d.mod(r),s.coeff=s.q.modInverse(s.p),setTimeout((function(){n()}),0)):setTimeout(e,0)},h=function t(){s.q=jt(),s.q.fromNumberAsync(r,1,i,(function(){s.q.subtract(Pt.ONE).gcda(o,(function(e){0==e.compareTo(Pt.ONE)&&s.q.isProbablePrime(10)?setTimeout(a,0):setTimeout(t,0)}))}))};setTimeout((function e(){s.p=jt(),s.p.fromNumberAsync(t-r,1,i,(function(){s.p.subtract(Pt.ONE).gcda(o,(function(t){0==t.compareTo(Pt.ONE)&&s.p.isProbablePrime(10)?setTimeout(h,0):setTimeout(e,0)}))}))}),0)}),0)},t.prototype.sign=function(t,e,n){var i=function(t,e){if(e<t.length+22)return console.error("Message too long for RSA"),null;for(var n=e-t.length-6,i="",r=0;r<n;r+=2)i+="ff";return Lt("0001"+i+"00"+t,16)}((ie[n]||"")+e(t).toString(),this.n.bitLength()/4);if(null==i)return null;var r=this.doPrivate(i);if(null==r)return null;var o=r.toString(16);return 1&o.length?"0"+o:o},t.prototype.verify=function(t,e,n){var i=Lt(e,16),r=this.doPublic(i);return null==r?null:function(t){for(var e in ie)if(ie.hasOwnProperty(e)){var n=ie[e],i=n.length;if(t.substr(0,i)==n)return t.substr(i)}return t}
/*!
Copyright (c) 2011, Yahoo! Inc. All rights reserved.
Code licensed under the BSD License:
http://developer.yahoo.com/yui/license.html
version: 2.9.0
*/(r.toString(16).replace(/^1f+00/,""))==n(t).toString()},t}();var ie={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"};var re={};re.lang={extend:function(t,e,n){if(!e||!t)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var i=function(){};if(i.prototype=e.prototype,t.prototype=new i,t.prototype.constructor=t,t.superclass=e.prototype,e.prototype.constructor==Object.prototype.constructor&&(e.prototype.constructor=e),n){var r;for(r in n)t.prototype[r]=n[r];var o=function(){},s=["toString","valueOf"];try{/MSIE/.test(navigator.userAgent)&&(o=function(t,e){for(r=0;r<s.length;r+=1){var n=s[r],i=e[n];"function"==typeof i&&i!=Object.prototype[n]&&(t[n]=i)}})}catch(t){}o(t.prototype,n)}}};
/**
 * @fileOverview
 * @name asn1-1.0.js
 * <AUTHOR>
 * @version asn1 1.0.13 (2017-Jun-02)
 * @since jsrsasign 2.1
 * @license <a href="https://kjur.github.io/jsrsasign/license/">MIT License</a>
 */
var oe={};void 0!==oe.asn1&&oe.asn1||(oe.asn1={}),oe.asn1.ASN1Util=new function(){this.integerToByteHex=function(t){var e=t.toString(16);return e.length%2==1&&(e="0"+e),e},this.bigIntToMinTwosComplementsHex=function(t){var e=t.toString(16);if("-"!=e.substr(0,1))e.length%2==1?e="0"+e:e.match(/^[0-7]/)||(e="00"+e);else{var n=e.substr(1).length;n%2==1?n+=1:e.match(/^[0-7]/)||(n+=2);for(var i="",r=0;r<n;r++)i+="f";e=new Pt(i,16).xor(t).add(Pt.ONE).toString(16).replace(/^-/,"")}return e},this.getPEMStringFromHex=function(t,e){return hextopem(t,e)},this.newObject=function(t){var e=oe.asn1,n=e.DERBoolean,i=e.DERInteger,r=e.DERBitString,o=e.DEROctetString,s=e.DERNull,a=e.DERObjectIdentifier,h=e.DEREnumerated,u=e.DERUTF8String,c=e.DERNumericString,f=e.DERPrintableString,l=e.DERTeletexString,d=e.DERIA5String,p=e.DERUTCTime,g=e.DERGeneralizedTime,v=e.DERSequence,m=e.DERSet,y=e.DERTaggedObject,b=e.ASN1Util.newObject,w=Object.keys(t);if(1!=w.length)throw"key of param shall be only one.";var S=w[0];if(-1==":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+S+":"))throw"undefined key: "+S;if("bool"==S)return new n(t[S]);if("int"==S)return new i(t[S]);if("bitstr"==S)return new r(t[S]);if("octstr"==S)return new o(t[S]);if("null"==S)return new s(t[S]);if("oid"==S)return new a(t[S]);if("enum"==S)return new h(t[S]);if("utf8str"==S)return new u(t[S]);if("numstr"==S)return new c(t[S]);if("prnstr"==S)return new f(t[S]);if("telstr"==S)return new l(t[S]);if("ia5str"==S)return new d(t[S]);if("utctime"==S)return new p(t[S]);if("gentime"==S)return new g(t[S]);if("seq"==S){for(var T=t[S],E=[],D=0;D<T.length;D++){var x=b(T[D]);E.push(x)}return new v({array:E})}if("set"==S){for(T=t[S],E=[],D=0;D<T.length;D++){x=b(T[D]);E.push(x)}return new m({array:E})}if("tag"==S){var O=t[S];if("[object Array]"===Object.prototype.toString.call(O)&&3==O.length){var A=b(O[2]);return new y({tag:O[0],explicit:O[1],obj:A})}var I={};if(void 0!==O.explicit&&(I.explicit=O.explicit),void 0!==O.tag&&(I.tag=O.tag),void 0===O.obj)throw"obj shall be specified for 'tag'.";return I.obj=b(O.obj),new y(I)}},this.jsonToASN1HEX=function(t){return this.newObject(t).getEncodedHex()}},oe.asn1.ASN1Util.oidHexToInt=function(t){for(var e="",n=parseInt(t.substr(0,2),16),i=(e=Math.floor(n/40)+"."+n%40,""),r=2;r<t.length;r+=2){var o=("00000000"+parseInt(t.substr(r,2),16).toString(2)).slice(-8);if(i+=o.substr(1,7),"0"==o.substr(0,1))e=e+"."+new Pt(i,2).toString(10),i=""}return e},oe.asn1.ASN1Util.oidIntToHex=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},n=function(t){var n="",i=new Pt(t,10).toString(2),r=7-i.length%7;7==r&&(r=0);for(var o="",s=0;s<r;s++)o+="0";i=o+i;for(s=0;s<i.length-1;s+=7){var a=i.substr(s,7);s!=i.length-7&&(a="1"+a),n+=e(parseInt(a,2))}return n};if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var i="",r=t.split("."),o=40*parseInt(r[0])+parseInt(r[1]);i+=e(o),r.splice(0,2);for(var s=0;s<r.length;s++)i+=n(r[s]);return i},oe.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if(void 0===this.hV||null==this.hV)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n=0,v="+this.hV;var t=this.hV.length/2,e=t.toString(16);if(e.length%2==1&&(e="0"+e),t<128)return e;var n=e.length/2;if(n>15)throw"ASN.1 length too long to represent by 8x: n = "+t.toString(16);return(128+n).toString(16)+e},this.getEncodedHex=function(){return(null==this.hTLV||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},oe.asn1.DERAbstractString=function(t){oe.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?this.setString(t):void 0!==t.str?this.setString(t.str):void 0!==t.hex&&this.setStringHex(t.hex))},re.lang.extend(oe.asn1.DERAbstractString,oe.asn1.ASN1Object),oe.asn1.DERAbstractTime=function(t){oe.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(t){return utc=t.getTime()+6e4*t.getTimezoneOffset(),new Date(utc)},this.formatDate=function(t,e,n){var i=this.zeroPadding,r=this.localDateToUTC(t),o=String(r.getFullYear());"utc"==e&&(o=o.substr(2,2));var s=o+i(String(r.getMonth()+1),2)+i(String(r.getDate()),2)+i(String(r.getHours()),2)+i(String(r.getMinutes()),2)+i(String(r.getSeconds()),2);if(!0===n){var a=r.getMilliseconds();if(0!=a){var h=i(String(a),3);s=s+"."+(h=h.replace(/[0]+$/,""))}}return s+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,e,n,i,r,o){var s=new Date(Date.UTC(t,e-1,n,i,r,o,0));this.setByDate(s)},this.getFreshValueHex=function(){return this.hV}},re.lang.extend(oe.asn1.DERAbstractTime,oe.asn1.ASN1Object),oe.asn1.DERAbstractStructured=function(t){oe.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,void 0!==t&&void 0!==t.array&&(this.asn1Array=t.array)},re.lang.extend(oe.asn1.DERAbstractStructured,oe.asn1.ASN1Object),oe.asn1.DERBoolean=function(){oe.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},re.lang.extend(oe.asn1.DERBoolean,oe.asn1.ASN1Object),oe.asn1.DERInteger=function(t){oe.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=oe.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new Pt(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.bigint?this.setByBigInteger(t.bigint):void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},re.lang.extend(oe.asn1.DERInteger,oe.asn1.ASN1Object),oe.asn1.DERBitString=function(t){if(void 0!==t&&void 0!==t.obj){var e=oe.asn1.ASN1Util.newObject(t.obj);t.hex="00"+e.getEncodedHex()}oe.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(t){this.hTLV=null,this.isModified=!0,this.hV=t},this.setUnusedBitsAndHexValue=function(t,e){if(t<0||7<t)throw"unused bits shall be from 0 to 7: u = "+t;var n="0"+t;this.hTLV=null,this.isModified=!0,this.hV=n+e},this.setByBinaryString=function(t){var e=8-(t=t.replace(/0+$/,"")).length%8;8==e&&(e=0);for(var n=0;n<=e;n++)t+="0";var i="";for(n=0;n<t.length-1;n+=8){var r=t.substr(n,8),o=parseInt(r,2).toString(16);1==o.length&&(o="0"+o),i+=o}this.hTLV=null,this.isModified=!0,this.hV="0"+e+i},this.setByBooleanArray=function(t){for(var e="",n=0;n<t.length;n++)1==t[n]?e+="1":e+="0";this.setByBinaryString(e)},this.newFalseArray=function(t){for(var e=new Array(t),n=0;n<t;n++)e[n]=!1;return e},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t&&t.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(t):void 0!==t.hex?this.setHexValueIncludingUnusedBits(t.hex):void 0!==t.bin?this.setByBinaryString(t.bin):void 0!==t.array&&this.setByBooleanArray(t.array))},re.lang.extend(oe.asn1.DERBitString,oe.asn1.ASN1Object),oe.asn1.DEROctetString=function(t){if(void 0!==t&&void 0!==t.obj){var e=oe.asn1.ASN1Util.newObject(t.obj);t.hex=e.getEncodedHex()}oe.asn1.DEROctetString.superclass.constructor.call(this,t),this.hT="04"},re.lang.extend(oe.asn1.DEROctetString,oe.asn1.DERAbstractString),oe.asn1.DERNull=function(){oe.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},re.lang.extend(oe.asn1.DERNull,oe.asn1.ASN1Object),oe.asn1.DERObjectIdentifier=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},n=function(t){var n="",i=new Pt(t,10).toString(2),r=7-i.length%7;7==r&&(r=0);for(var o="",s=0;s<r;s++)o+="0";i=o+i;for(s=0;s<i.length-1;s+=7){var a=i.substr(s,7);s!=i.length-7&&(a="1"+a),n+=e(parseInt(a,2))}return n};oe.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.setValueOidString=function(t){if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var i="",r=t.split("."),o=40*parseInt(r[0])+parseInt(r[1]);i+=e(o),r.splice(0,2);for(var s=0;s<r.length;s++)i+=n(r[s]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=i},this.setValueName=function(t){var e=oe.asn1.x509.OID.name2oid(t);if(""===e)throw"DERObjectIdentifier oidName undefined: "+t;this.setValueOidString(e)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?t.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(t):this.setValueName(t):void 0!==t.oid?this.setValueOidString(t.oid):void 0!==t.hex?this.setValueHex(t.hex):void 0!==t.name&&this.setValueName(t.name))},re.lang.extend(oe.asn1.DERObjectIdentifier,oe.asn1.ASN1Object),oe.asn1.DEREnumerated=function(t){oe.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=oe.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new Pt(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},re.lang.extend(oe.asn1.DEREnumerated,oe.asn1.ASN1Object),oe.asn1.DERUTF8String=function(t){oe.asn1.DERUTF8String.superclass.constructor.call(this,t),this.hT="0c"},re.lang.extend(oe.asn1.DERUTF8String,oe.asn1.DERAbstractString),oe.asn1.DERNumericString=function(t){oe.asn1.DERNumericString.superclass.constructor.call(this,t),this.hT="12"},re.lang.extend(oe.asn1.DERNumericString,oe.asn1.DERAbstractString),oe.asn1.DERPrintableString=function(t){oe.asn1.DERPrintableString.superclass.constructor.call(this,t),this.hT="13"},re.lang.extend(oe.asn1.DERPrintableString,oe.asn1.DERAbstractString),oe.asn1.DERTeletexString=function(t){oe.asn1.DERTeletexString.superclass.constructor.call(this,t),this.hT="14"},re.lang.extend(oe.asn1.DERTeletexString,oe.asn1.DERAbstractString),oe.asn1.DERIA5String=function(t){oe.asn1.DERIA5String.superclass.constructor.call(this,t),this.hT="16"},re.lang.extend(oe.asn1.DERIA5String,oe.asn1.DERAbstractString),oe.asn1.DERUTCTime=function(t){oe.asn1.DERUTCTime.superclass.constructor.call(this,t),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{12}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date))},re.lang.extend(oe.asn1.DERUTCTime,oe.asn1.DERAbstractTime),oe.asn1.DERGeneralizedTime=function(t){oe.asn1.DERGeneralizedTime.superclass.constructor.call(this,t),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{14}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date),!0===t.millis&&(this.withMillis=!0))},re.lang.extend(oe.asn1.DERGeneralizedTime,oe.asn1.DERAbstractTime),oe.asn1.DERSequence=function(t){oe.asn1.DERSequence.superclass.constructor.call(this,t),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++){t+=this.asn1Array[e].getEncodedHex()}return this.hV=t,this.hV}},re.lang.extend(oe.asn1.DERSequence,oe.asn1.DERAbstractStructured),oe.asn1.DERSet=function(t){oe.asn1.DERSet.superclass.constructor.call(this,t),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var n=this.asn1Array[e];t.push(n.getEncodedHex())}return 1==this.sortFlag&&t.sort(),this.hV=t.join(""),this.hV},void 0!==t&&void 0!==t.sortflag&&0==t.sortflag&&(this.sortFlag=!1)},re.lang.extend(oe.asn1.DERSet,oe.asn1.DERAbstractStructured),oe.asn1.DERTaggedObject=function(t){oe.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,n){this.hT=e,this.isExplicit=t,this.asn1Object=n,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=n.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.tag&&(this.hT=t.tag),void 0!==t.explicit&&(this.isExplicit=t.explicit),void 0!==t.obj&&(this.asn1Object=t.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},re.lang.extend(oe.asn1.DERTaggedObject,oe.asn1.ASN1Object);var se,ae,he=(se=function(t,e){return se=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},se(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}se(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),ue=function(t){function e(n){var i=t.call(this)||this;return n&&("string"==typeof n?i.parseKey(n):(e.hasPrivateKeyProperty(n)||e.hasPublicKeyProperty(n))&&i.parsePropertiesFrom(n)),i}return he(e,t),e.prototype.parseKey=function(t){try{var e=0,n=0,i=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(t)?bt(t):wt.unarmor(t),r=It.decode(i);if(3===r.sub.length&&(r=r.sub[2].sub[0]),9===r.sub.length){e=r.sub[1].getHexStringValue(),this.n=Lt(e,16),n=r.sub[2].getHexStringValue(),this.e=parseInt(n,16);var o=r.sub[3].getHexStringValue();this.d=Lt(o,16);var s=r.sub[4].getHexStringValue();this.p=Lt(s,16);var a=r.sub[5].getHexStringValue();this.q=Lt(a,16);var h=r.sub[6].getHexStringValue();this.dmp1=Lt(h,16);var u=r.sub[7].getHexStringValue();this.dmq1=Lt(u,16);var c=r.sub[8].getHexStringValue();this.coeff=Lt(c,16)}else{if(2!==r.sub.length)return!1;if(r.sub[0].sub){var f=r.sub[1].sub[0];e=f.sub[0].getHexStringValue(),this.n=Lt(e,16),n=f.sub[1].getHexStringValue(),this.e=parseInt(n,16)}else e=r.sub[0].getHexStringValue(),this.n=Lt(e,16),n=r.sub[1].getHexStringValue(),this.e=parseInt(n,16)}return!0}catch(t){return!1}},e.prototype.getPrivateBaseKey=function(){var t={array:[new oe.asn1.DERInteger({int:0}),new oe.asn1.DERInteger({bigint:this.n}),new oe.asn1.DERInteger({int:this.e}),new oe.asn1.DERInteger({bigint:this.d}),new oe.asn1.DERInteger({bigint:this.p}),new oe.asn1.DERInteger({bigint:this.q}),new oe.asn1.DERInteger({bigint:this.dmp1}),new oe.asn1.DERInteger({bigint:this.dmq1}),new oe.asn1.DERInteger({bigint:this.coeff})]};return new oe.asn1.DERSequence(t).getEncodedHex()},e.prototype.getPrivateBaseKeyB64=function(){return vt(this.getPrivateBaseKey())},e.prototype.getPublicBaseKey=function(){var t=new oe.asn1.DERSequence({array:[new oe.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new oe.asn1.DERNull]}),e=new oe.asn1.DERSequence({array:[new oe.asn1.DERInteger({bigint:this.n}),new oe.asn1.DERInteger({int:this.e})]}),n=new oe.asn1.DERBitString({hex:"00"+e.getEncodedHex()});return new oe.asn1.DERSequence({array:[t,n]}).getEncodedHex()},e.prototype.getPublicBaseKeyB64=function(){return vt(this.getPublicBaseKey())},e.wordwrap=function(t,e){if(!t)return t;var n="(.{1,"+(e=e||64)+"})( +|$\n?)|(.{1,"+e+"})";return t.match(RegExp(n,"g")).join("\n")},e.prototype.getPrivateKey=function(){var t="-----BEGIN RSA PRIVATE KEY-----\n";return t+=e.wordwrap(this.getPrivateBaseKeyB64())+"\n",t+="-----END RSA PRIVATE KEY-----"},e.prototype.getPublicKey=function(){var t="-----BEGIN PUBLIC KEY-----\n";return t+=e.wordwrap(this.getPublicBaseKeyB64())+"\n",t+="-----END PUBLIC KEY-----"},e.hasPublicKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")},e.hasPrivateKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")&&t.hasOwnProperty("d")&&t.hasOwnProperty("p")&&t.hasOwnProperty("q")&&t.hasOwnProperty("dmp1")&&t.hasOwnProperty("dmq1")&&t.hasOwnProperty("coeff")},e.prototype.parsePropertiesFrom=function(t){this.n=t.n,this.e=t.e,t.hasOwnProperty("d")&&(this.d=t.d,this.p=t.p,this.q=t.q,this.dmp1=t.dmp1,this.dmq1=t.dmq1,this.coeff=t.coeff)},e}(ne),ce="undefined"!=typeof process?null===(ae=process.env)||void 0===ae?void 0:ae.npm_package_version:void 0,fe=function(){function t(t){void 0===t&&(t={}),t=t||{},this.default_key_size=t.default_key_size?parseInt(t.default_key_size,10):1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}return t.prototype.setKey=function(t){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new ue(t)},t.prototype.setPrivateKey=function(t){this.setKey(t)},t.prototype.setPublicKey=function(t){this.setKey(t)},t.prototype.decrypt=function(t){try{return this.getKey().decrypt(mt(t))}catch(t){return!1}},t.prototype.encrypt=function(t){try{return vt(this.getKey().encrypt(t))}catch(t){return!1}},t.prototype.sign=function(t,e,n){try{return vt(this.getKey().sign(t,e,n))}catch(t){return!1}},t.prototype.verify=function(t,e,n){try{return this.getKey().verify(t,mt(e),n)}catch(t){return!1}},t.prototype.getKey=function(t){if(!this.key){if(this.key=new ue,t&&"[object Function]"==={}.toString.call(t))return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},t.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},t.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},t.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},t.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},t.version=ce,t}(),le=function(){return n((function e(n){t(this,e),this.url="",this.sourceConfig={devMode:!1,debug:"",category:"",appnm:""},this.userConfig={channel:"",traceId:_(),pageTraceId:K(),uid:j(u)||"",tenantId:"",envVar:""},this.config={},this.apiPaths={"cp-admin":{get:"/admin/v1/common/log/hand/eventV1",post:"/admin/v1/common/log/hand/eventV2"},common:{get:"/h5/v1/common/log/hand/eventV1",post:"/h5/v1/common/log/hand/eventV2"}},n?this.set(n):this.config=this.sourceConfig}),[{key:"set",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;for(var n in t)t.hasOwnProperty(n)&&("object"!==a(t[n])||t[n]instanceof RegExp||t[n]instanceof Array?this.userConfig[n]=t[n]:this.userConfig[n]=V(this.userConfig[n],t[n]));var i=this.userConfig.devMode;if(t.uid)try{var r=new fe;r.setPublicKey("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCgSKVijH5762x3ap6A20W3dXw2+n4Ok/LGag/YpwCsdcVQ/Wqg//HNuKalNaDO2C9JPunLcUFiDjSUAU7SipxKQIol8MsSRZNU84mK8f8667R3GQG76xLPvG1RB9Ff5Pe2N5e2aush68JFPZ+6KuALrOtpUZHAUbpMRzgvEqGdmwIDAQAB");var o=r.encrypt(String(t.uid));this.userConfig.uid=o,C(u,o,1576800)}catch(t){e.reportSystemError(t)}this.update(),i&&t.uid&&ot(this)}},{key:"get",value:function(t){return t?this.config[t]||"":this.config}},{key:"getApiPath",value:function(t){var e=this.get("appnm"),n="cp-admin"===e?e:"common",i=this.apiPaths[n][t];return this.url=g[n],"".concat(this.url).concat(i)}},{key:"update",value:function(){for(var t in this.config=this.sourceConfig,this.userConfig){var e=this.userConfig[t];"object"!=a(e)||e instanceof RegExp||e instanceof Array?this.config[t]=e:this.config[t]=V(this.config[t],this.userConfig[t])}}},{key:"updateReportDomain",value:function(t){this.url=t}},{key:"getReportDomain",value:function(){return this.url}}])}(),de=window.navigator.userAgent,pe=!!de.match(/(msie) (\d+)|Trident\/(d+)/i),ge=/firefox/i.test(de),ve=!pe&&window.indexedDB,me=window.IDBFactory,ye=ve&&S(ve.open)&&ve.constructor===me,be=!1,we=!1,Se=!1,Te=null,Ee=null,De=null,xe=0,Oe=function(){return new Promise((function(t){if(we)return t(!1);if(ge)return v("firefox内不启用indexedDB"),we=!0,t(!1);if(!ye)return we=!0,t(!1);if(be)return t(!0);try{var e=ve.open("_blmsdk_db",1e3);e.onsuccess=function(e){console.log("cacheRetry: db open succeed"),be=!0,Ee=e.target.result,t(!0)},e.onupgradeneeded=function(t){Ee=t.target.result,(De=Ee.createObjectStore("cache",{keyPath:"id",autoIncrement:!0})).createIndex("data","data",{unique:!1}),De.createIndex("type","type",{unique:!1})}}catch(e){return we=!0,be=!1,t(!1)}}))},Ae=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Oe().then((function(e){if(e){console.log("cacheRetry: start checking db...");var n=Ee.transaction(["cache"],"readwrite").objectStore("cache"),i=n.openCursor(),r=[];i.onsuccess=function(e){var i=e.target.result;i?(r.push(i.value),i.continue()):(xe=r.length,console.log("cacheRetry: sending data...".concat(r.length," ")),r.forEach((function(e){!function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.data,i=t.type;console.log("cacheRetry: send indexedDB Cache: ",n);var r=q().toString(16);if("get"===i){var o="".concat(tt(null==e?void 0:e.appnm),"/v1/common/log/hand/eventV1?c=").concat(encodeURIComponent(Y(n)),"&t=cache&rnd=").concat(r);it({type:"get",url:o})}else try{window.navigator.sendBeacon("".concat(tt(null==e?void 0:e.appnm),"/v1/common/log/hand/eventV2?t=cache&rnd=").concat(r),n)}catch(t){console.log("当前不支持sendBeacon")}}(e,t),n.delete(e.id)})))}}}))},Ie=function(t,e){Oe().then((function(n){if(n){var i=Ee.transaction(["cache"],"readwrite").objectStore("cache");xe>100&&(console.log("idb clear"),i.clear()),console.log("cacheRetry: add data... "),i.add({type:e,data:JSON.stringify(t)})}}))},Re=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};!Se&&ye&&(Se=!0,Te=+new Date,Oe().then((function(e){e&&setTimeout((function e(){var n;console.log("cacheRetry: check net status...");var i="_blmsdk_poll_img",r=new Image;window[i]=r,r.onload=function(){var e;Ae({appnm:null==t||null===(e=t.statCommon)||void 0===e?void 0:e.appnm}),Se=!1,console.log("cacheRetry: net status OK, check indexedDB..."),window[i]=null};var o=function(){console.log("cacheRetry: net status ERROR, check again after 5s..."),(new Date).getTime()-Te>3e5?console.log("cacheRetry: net status ERROR, check overtime, stop"):(setTimeout(e,6e3),window[i]=null)};r.onabort=o,r.onerror=o,r.src="".concat(tt(null==t||null===(n=t.statCommon)||void 0===n?void 0:n.appnm),"/v1/common/log/hand/heart")}),6e3)})))};Oe();var Be,Ne=window.navigator.userAgent,Pe=!!Ne.match(/(msie) (\d+)|Trident\/(d+)/i),Ve=!/Chrome/.test(Ne)&&!Pe&&/Mozilla.+AppleWebKit.+Version.+Safari.+/.test(Ne),Me=(Be=window.XMLHttpRequest)&&"withCredentials"in new Be,Ce=function(){return n((function e(n){t(this,e),this.cfgManager=n,this.reportCount=0}),[{key:"getRndUrl",value:function(t){var e=q().toString(16)+this.reportCount++;return"".concat(t,"?rnd=").concat(e)}},{key:"isExceedMaxUrlLen",value:function(t){for(var e=t.length,n=0;n<e;n++){t.charCodeAt(n)>127&&e++}var i=2024;return/trident/i.test(Ne)||/msie/i.test(Ne)||!/mozilla.+webkit.+chrome.+/i.test(Ne)||(i=6e3),1.5*e>i}},{key:"reportWithHTTP",value:function(t){var e=!Ve,n=this.cfgManager.getApiPath("post");e?this.sendWithGetMethod(t):Me?this.sendWithPostMethod(n,t):Pe&&window.XDomainRequest?this.sendWithIEXDR(n,t):window.navigator.sendBeacon&&this.sendWithNavBeacon(n,t)}},{key:"sendWithGetMethod",value:function(t){console.log("data: ",t);var e=JSON.stringify(t),n=this.cfgManager.getApiPath("post");if(this.isExceedMaxUrlLen(e))this.sendWithPostMethod(n,t);else{var i=Y(e),r=q().toString(16)+this.reportCount++,o="".concat(this.cfgManager.getApiPath("get"),"?c=").concat(encodeURIComponent(i),"&rnd=").concat(r),s=this;this.sendWithCorsAjax(o,null,{method:"GET",cb:function(e){1!==e&&(s.sendWithNavBeacon(n,t),Ie(t,"get"),Re(t))}})}}},{key:"sendWithPostMethod",value:function(t,e){if(!Me)return!1;var n=this,i=JSON.stringify(e);this.sendWithCorsAjax(t,i,{method:"POST",cb:function(i){1!==i&&(n.sendWithNavBeacon(t,e),Ie(e,"post"),Re(e))}})}},{key:"sendWithIEXDR",value:function(t,e){if(!window.XDomainRequest)return!1;try{var n=new XDomainRequest;n.open(POST,getRndUrl(t),!0),n.onload=function(){};var i=this;n.onerror=n.ontimeout=function(){i.sendWithNavBeacon(t,e)},n.timeout=5e3,n.send(JSON.stringify(e))}catch(t){v(t)}}},{key:"sendWithNavBeacon",value:function(t,e){var n=window.navigator.sendBeacon;if(!n)return!1;n&&n.call(window.navigator,this.getRndUrl(t),JSON.stringify(e))}},{key:"sendWithCorsAjax",value:function(t,e,n){if(!Me)return!1;var i=n&&n.method||"GET";it({type:i,url:t,data:e,success:function(t){S(n.cb)&&n.cb(t.code)},fail:function(){S(n.cb)&&n.cb(0)}})}},{key:"send",value:function(t){this.reportWithHTTP(t)}}])}(),ke=function(){return window.location.href},je=function(){return location.search};window.BlmAnalysisMS=window.BlmAnalysisMS||"".concat(G()),window.BlmAnalysisSeq=window.BlmAnalysisSeq||0;var Le=function(){return n((function e(n){t(this,e),this.network=new Ce(n),this.cfgManager=n,this.pvNum=0,this.plStack={},this.reportCount=0,this.currentPageOps={},this.pageTraceId=n.get("pageTraceId")}),[{key:"addPageLeave",value:function(t,e){this.plStack[t]=V({},e)}},{key:"sendPageLeave",value:function(t){var e=t.timeStamp||q();for(var n in this.plStack){var i=this.plStack[n].evs[0];i.type="pl",i.duration=e-i.ts,i.ts=e,i.seq=Z(),i.isAuto=t.isAuto||1,i.innerData=V({seq:++window.BlmAnalysisSeq},i.innerData),delete i.firstPV,this.network.send(this.plStack[n]),delete this.plStack[n]}}},{key:"validationParams",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=!0,n=t.pageId;return t.eventId||(v("eventId不得为空"),e=!1),w(n)||(v("传入的eventId必须是字符类型"),e=!1),n||(v("pageId不得为空"),e=!1),w(n)||(v("传入的pageId必须是字符类型"),e=!1),e}},{key:"parse",value:function(){var t=this.cfgManager.get(),e=t.traceId;t.pageTraceId;var n=t.devMode,i=t.appnm,r=t.category,o=t.tenantId,s=t.channel,a=t.envVar,h=t.uid,u=t.debug,c=M(),f=c.isIOS,l=c.isAndroid,d=c.isWindows,p=c.isMac,g=0;g=f?1:l?2:d?3:p?4:5;var v={nt:0,screen:window.screen.width+"*"+window.screen.height,traceId:e,sdkVersion:"1.1.2",sdkEnv:n?"offline":"online",misid:z(0),statCommon:{appnm:i,category:r,diu:F(),ts:(new Date).getTime(),tenantId:o,channel:s,uid:h,pf:g,envVar:a}};return u&&(v.debug=u),v}},{key:"webEvsWrapper",value:function(t){var e=t.ext,n=t.type,i=t.isAuto,r=void 0===i?0:i,o=t.pageId,s=t.eventId,a=t.firstPV,h=document.referrer,u=this.pageTraceId||this.cfgManager.get("pageTraceId"),c={seq:Z(),pageId:o,eventId:s,pageTraceId:u,url:location.href,type:n,ext:e,ts:(new Date).getTime(),innerData:{seq:++window.BlmAnalysisSeq,misid:window.BlmAnalysisMS}},f="pv"===n;if(f&&(c.isAuto=r,h&&f&&(c.referrer=h),c.firstPV=a||!1),e){var l=JSON.stringify(e).length;l>=7e3&&(v("lab超过长度限制，已裁切。原ext：".concat(e)),c.ext={overLen:l})}return c}},{key:"dataWrapper",value:function(t){return E(t)||(t=[t]),o(o({},this.parse()),{},{evs:t})}},{key:"getCurrentPageOps",value:function(){var t=ke(),e=this.currentPageOps;return e[t]?e[t]:null}},{key:"pageView",value:function(t,e){if(this.validationParams(t)){this.sendPageLeave({isAuto:1}),this.pageTraceId=K();var n=this.webEvsWrapper(t),r=this.dataWrapper(n),o=this.cfgManager.get().category;this.addPageLeave(o,r);var s=ke();this.currentPageOps=i({},s,t),this.network.send(r)}}},{key:"pageLeave",value:function(t){var e=t.isAuto,n=void 0===e?0:e,i=t.timeStamp;this.sendPageLeave({isAuto:n,ts:i||""})}},{key:"moduleClick",value:function(t){if(this.validationParams(t)){var e=this.webEvsWrapper(t),n=this.dataWrapper(e);this.network.send(n)}}},{key:"moduleExposure",value:function(t){if(this.validationParams(t)){var e=this.webEvsWrapper(t),n=this.dataWrapper(e);this.network.send(n)}}}])}(),qe="blm-exposure",He="blm-click",Ue=[],_e={},Ke=null,Fe={rate:.5,delay:0,repeated:!0},We={isStarted:!1,init:function(){if(("MutationObserver"in window||"WebKitMutationObserver"in window||"MozMutationObserver"in window)&&"IntersectionObserver"in window){var t=this;$((function(){var e=t.getElesByEventName();t.addObserveByNodes(e),Ge.init()})),this.isStarted=!0}else console.log("当前浏览器不支持声明式埋点")},start:function(){Q(Ue,(function(t){var e=t.config,n=t.ele,i=_e[e.rate];i&&D(n)&&i.observe(n)}))},stop:function(){Q(Ue,(function(t){var e=t.config,n=t.ele,i=_e[e.rate];i&&D(n)&&(i.unobserve(n),t.timer&&(clearTimeout(t.timer),t.timer=null))}))},clear:function(){this.stop(),_e={},Ue=[];var t=this.getElesByEventName();this.addObserveByNodes(t)},removeWatchEle:function(t){var e=null,n=-1;if(Q(Ue,(function(i,r){t===i.ele&&(e=i,n=r)})),e){var i=e.config,r=_e[i.rate];r&&D(t)&&(r.unobserve(t),e.timer&&(clearTimeout(e.timer),e.timer=null),n>-1&&Ue.splice(n,1))}},getElesByEventName:function(t){return(t||document).querySelectorAll("[".concat(qe,"]"))},addObserveByNodes:function(t){t&&t.length&&Q(t,(function(t){if(1===t.nodeType&&t.hasAttribute(qe)){var e={},n=null;try{n=JSON.parse(t.getAttribute(qe))}catch(e){return void console.error("节点 :",t,"的blm-exposure值不是标准的JSON格式：",t.getAttribute(qe))}n&&(e.config=V(Fe,n),e.ele=t,We.addOrUpdateWatchEle(e))}}))},validationParams:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=!0,n=t.pageId;return t.eventId||(Logger.ignore("eventId不得为空"),e=!1),w(n)||(Logger.ignore("传入的eventId必须是字符类型"),e=!1),n||(Logger.ignore("pageId不得为空"),e=!1),w(n)||(Logger.ignore("传入的pageId必须是字符类型"),e=!1),e},getIntersection:function(t){var e=t.rate,n=null,i=this;return n=_e[e]?_e[e]:_e[e]=new IntersectionObserver((function(){i.exposure.apply(i,arguments)}),{threshold:e}),n},addOrUpdateWatchEle:function(t){var e=t.ele,n=t.config;if(this.validationParams(n)){var i=this.getEleOption(e);if(i)(i=V(i,t)).config.repeated&&(i.config.isSend=!1);else this.getIntersection(n).observe(e),Ue.push(t)}},getEleOption:function(t){var e=null;return Q(Ue,(function(n){t===n.ele&&(e=n)})),e},exposure:function(t){var e=this;Q(t,(function(t){var n=t.target,i=e.getEleOption(n);if(!1!==t.isIntersecting&&i&&!i.config.isSend){if(t.intersectionRatio>=i.config.rate){i.timer&&(clearTimeout(i.timer),i.timer=null);var r=1e3*i.delay;i.timer=setTimeout((function(){var t=n.getBoundingClientRect(),i=e.getEleOption(n),r=t.width,o=t.height;if(r&&o&&i&&!i.config.isSend){var s=i.config||{},a=s.pageId,h=s.eventId,u=s.ext;Ke.moduleExposure({type:"exposure",pageId:a,eventId:h,ext:u}),i.config.isSend=!0,i.config.repeated&&(i.config.isSend=!1)}}),r)}}else i&&i.timer&&(clearTimeout(i.timer),i.timer=null)}))},addModuleExposure:function(t,e){if(this.isStarted&&(e.pageId,e.eventId,e.ext,D(t)&&this.validationParams(e))){var n,i={ele:t,config:(n=e,n&&"[object Object]"===m.call(n)?e:{})},r=this.getEleOption(t);r?(r=V(r,i)).config.repeated&&(r.config.isSend=!1):(i.config=V(Fe,i.config),this.addOrUpdateWatchEle(i))}},removeModuleExposure:function(t){this.isStarted&&D(t)&&this.removeWatchEle(t)}},Ge={init:function(){var t=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver;this.mutationObserver=new t(this.listenNodesChange),this.mutationObserver.observe(document.body,{attributes:!0,childList:!0,subtree:!0,attributeOldValue:!0})},listenNodesChange:function(t){Q(t,(function(t){switch(t.type){case"childList":t.removedNodes&&t.removedNodes.length?Q(t.removedNodes,(function(t){if(1===t.nodeType){We.removeWatchEle(t);var e=We.getElesByEventName(t);e.length&&Q(e,(function(t){We.removeWatchEle(t)}))}})):t.addedNodes&&t.addedNodes.length&&(We.addObserveByNodes(t.addedNodes),Q(t.addedNodes,(function(t){if(1===t.nodeType){var e=We.getElesByEventName(t);We.addObserveByNodes(e)}})));break;case"attributes":if(!t.attributeName)return!1;var e=t.target,n=t.attributeName;if(!w(n)||-1===n.indexOf(qe))return;var i=null;try{i=JSON.parse(e.getAttribute(qe))}catch(t){return void console.error("节点 :",e,"的lx-mv值不是标准的JSON格式：",e.getAttribute(qe))}if(i){var r=We.getEleOption(e)||{ele:e,config:Fe},o=V(Fe,i),s=V(r,{config:o});We.removeWatchEle(e),We.addOrUpdateWatchEle(s)}}}))}},ze={init:function(){var t=this;$((function(){P(document.body,"click",(function(e){var n=t.findParentNode(e.target,(function(e){return!!t.hasLXAttr(e,He)&&e.getAttribute(He)}));if(n&&t.hasLXAttr(n,He))try{var i=JSON.parse(n.getAttribute(He)),r={pageId:i.pageId,eventId:i.eventId,ext:i.ext,type:"click"};Ke.moduleClick(r)}catch(t){console.error("节点 :",n,"的blm-click值不是标准的JSON格式：",n.getAttribute(He))}}))}))},hasLXAttr:function(t,e){return t.getAttribute&&t.getAttribute(e)},findParentNode:function(t,e){var n=t;return!(!n||n===document.body||!n.parentNode)&&(e(n)?n:ze.findParentNode(n.parentNode,e))}},Ze=null,Je=null,Xe=null,Ye=!1,Qe=null,$e=function(t){if(!(!1 in document)&&!(!1 in window)){"visible"!==document.visibilityState&&(Ze=q());P(document,"visibilitychange",(function(){"visible"===document.visibilityState?Je=setTimeout((function(){if(!Ye&&"visible"===document.visibilityState&&(We.start(),q()-Ze>=2e3&&(clearTimeout(Xe),t.getCurrentPageOps()))){var e=o(o({},t.getCurrentPageOps()),{},{isAuto:1,firstPV:!1,type:"pv"});t.pageView(e,!0)}}),1e3):(Ze=q(),Xe=setTimeout((function e(){if(!Ye&&"visible"!==document.visibilityState)if(We.stop(),q()-Ze>=2e3){if(clearTimeout(Je),t.getCurrentPageOps()){var n=o(o({},t.getCurrentPageOps()),{},{isAuto:1,timeStamp:Ze});t.pageLeave(n)}}else Xe=setTimeout(e,1e3)}),1e3))})),P(window,"resize",(function(){Ye=!0,clearTimeout(Qe),Qe=setTimeout((function(){Ye=!1}),2e3)})),function(t){var e=location.href,n=window.history.pushState,i=window.history.replaceState;S(window.history.pushState)&&(window.history.pushState=function(){n.apply(window.history,arguments),t(e),e=location.href}),S(window.history.replaceState)&&(window.history.replaceState=function(){i.apply(window.history,arguments),t(e),e=location.href});var r=null;r=window.document.documentMode?"hashchange":n?"popstate":"hashchange",P(window,r,(function(){t(e),e=location.href}))}((function(t){t!==location.href&&We.clear()}))}},tn=/__blmValidation=([^&]*)(&|$)/i;var en=["uid","channel","tenantId","envVar","devMode","category","appnm"],nn=function(){return n((function e(n,i){t(this,e);var r=new le(n);this.eventManager=new Le(r),this.isStarted=!1,this.cfgManager=r}),[{key:"pageView",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.pageId,n=t.eventId,i=t.ext,r=void 0===i?{}:i;this.eventManager.pageView({type:"pv",firstPV:!0,pageId:e,eventId:n,ext:r})}},{key:"pageLeave",value:function(){var t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})||{},e=t.eventId,n=t.pageId,i=t.ext,r=void 0===i?{}:i;this.eventManager.pageLeave({type:"pl",pageId:n,eventId:e,ext:r})}},{key:"moduleClick",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.eventId,n=t.pageId,i=t.ext,r=void 0===i?{}:i;this.eventManager.moduleClick({type:"click",pageId:n,eventId:e,ext:r})}},{key:"moduleExposure",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.eventId,n=t.pageId,i=t.ext,r=void 0===i?{}:i;this.eventManager.moduleExposure({type:"exposure",pageId:n,eventId:e,ext:r})}},{key:"addModuleExposure",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};We.addModuleExposure(t,e)}},{key:"removeModuleExposure",value:function(t){We.removeModuleExposure(t)}},{key:"set",value:function(t,e){-1!==en.indexOf(t)?this.cfgManager.set(i({},t,e),this.errManager):console.warn("禁止设置该公参")}}])}(),rn=new nn;return rn.createCase=nn,rn.start=function(t){var e,n,i,r,o=window.navigator.userAgent;(R.forEach((function(t){-1===o.indexOf(t)||console.warn("当前环境被判断为爬虫，监控功能关闭")})),this.isStarted)||(this.isStarted=!0,t&&this.cfgManager.set(t),ot(this.cfgManager),function(t){var e="";try{e=decodeURIComponent(je())}catch(t){console.error("产品业务埋点SDK解码异常")}var n=e.match(tn)||[],i=j(l)||"",r=n&&n[1]||i||"";r&&(C(l,r,30),t.set({debug:r}))}(this.cfgManager),e=this.eventManager,n=M(),i=n.isMobile,r=n.isIOS,P(i&&r?"pagehide":"beforeunload",(function(){e.sendPageLeave()})),$e(this.eventManager),function(t){Ke=t,We.init(),ze.init()}(this.eventManager),Ae({appnm:this.cfgManager.get("appnm")}))},rn}();
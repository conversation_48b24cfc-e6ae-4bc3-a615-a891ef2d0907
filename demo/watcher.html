<!DOCTYPE html>
<html>
<head>
    <title>声明式埋点上报</title>
    <meta charset="utf8">
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <script src="../dist/blm-analysis.umd.min.js"></script>
</head>

<body style="height: 1000px">
    <div blm-click='{ "eventId": "event1111", "pageId": "page1111"}'>点我</div>
    <div blm-exposure='{"eventId": "event2222", "pageId": "page2222", "ext": "{a: 1}"}'>曝光我</div>
    <div id="exposure1">曝光它</div>

    <script>
        window.BlmAnalysis.start({
            devMode: true,
            category: 'leopard',
            appnm: 'cp-admin'
        });

        const el = document.createElement('div');
        el.setAttribute('blm-exposure', '{ "eventId": "event3333", "pageId": "page3333"}');
        el.innerHTML = '曝光我2222';
        document.body.appendChild(el);

        window.BlmAnalysis.addModuleExposure(
            document.getElementById('exposure1'),    
            {
                eventId: 'event4444',
                pageId: 'page4444',
                ext: {
                    a: 1
                }
            }
        );

        setTimeout(function() {
            window.BlmAnalysis.removeModuleExposure(document.getElementById('exposure1'));
        }, 2000)
    </script>
</body>
</html>

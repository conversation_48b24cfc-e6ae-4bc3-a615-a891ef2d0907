<!DOCTYPE html>
<html>
<head>
    <title>页面埋点上报</title>
    <meta charset="utf8">
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <script src="../dist/blm-analysis.umd.min.js"></script>
</head>

<body>
    <script>
        const blmMonitor = window.BlmAnalysis.start({
            devMode: true,
            category: 'leopard',
            appnm: 'cp'
        });

        var a = '111'

        // for (let i = 0; i < 1000; i++) {
        //     a = i + a;
        // }

        window.BlmAnalysis.set('uid', '1111');

        window.BlmAnalysis.pageView({ eventId: 'eventId3333', pageId: '111', ext: { c: 1, a }});

        setTimeout(() => {
            window.BlmAnalysis.moduleClick({ eventId: 'eventId222', pageId: '111', ext: { d: 2 }});
        }, 1000);

        // window.BlmAnalysis.moduleClick({ eventId: 'eventId222', pageId: '111', ext: { d: 2 }});

        window.BlmAnalysis.pageLeave();
    </script>
</body>

</html>

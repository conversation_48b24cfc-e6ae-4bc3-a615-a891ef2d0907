import { extend, setCookie, getCookie, getTraceId, getPageTraceId } from './utils';
import { BLM_UID, ONLINE_URL } from './constant';
import validateUserWhite from './white';

import JSEncrypt from 'jsencrypt';

// uid过期时间，默认3年
const THREE_YEARS_IN_MINUTES = 3 * 365 * 24 * 60;

// 公钥
const pubPrivateKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCgSKVijH5762x3ap6A20W3dXw2+n4Ok/LGag/YpwCsdcVQ/Wqg//HNuKalNaDO2C9JPunLcUFiDjSUAU7SipxKQIol8MsSRZNU84mK8f8667R3GQG76xLPvG1RB9Ff5Pe2N5e2aush68JFPZ+6KuALrOtpUZHAUbpMRzgvEqGdmwIDAQAB';

/**
 * 配置管理类
 */
class ConfigManager {
    constructor(config) {
        this.url = ''
    

        this.sourceConfig = {
            devMode: false,  // 是否上报到线下环境，false: 线上环境，true：线下环境
            debug: '',       // 是否启用埋点验证模式，验证模式会写入用户账户名
            category: '',    // 业务线
            appnm: ''        // 应用名
        };
        // 用户传入的配置信息，存有一份默认值
        this.userConfig = {
            channel: '',                        // 渠道（web、h5）
            traceId: getTraceId(),              // 容器串联标识
            pageTraceId: getPageTraceId(),      // 页面串联标识
            uid: getCookie(BLM_UID) || '', // 用户id，先从初始化配置里获取，如果获取不到从cookie里获取
            tenantId: '',                       // 租户ID
            envVar: ''                          // 项目环境
        };

        // 初始化配置
        this.config = {};

        // 请求路径
        this.apiPaths = {
            'cp-admin': {
                get: '/admin/v1/common/log/hand/eventV1',      // GET请求接口
                post: '/admin/v1/common/log/hand/eventV2',       // POST请求接口
            },
            common: {
                get: '/h5/v1/common/log/hand/eventV1',      // GET请求接口
                post: '/h5/v1/common/log/hand/eventV2',       // POST请求接口
            }
        };

        if (config) {
            // 如果业务传入配置，设置配置
            this.set(config);
        } else {
            // 如果未传入配置，使用默认配置
            this.config = this.sourceConfig;
        }
    }
    // 设置配置
    set(data = {}, errManager) {
        for (let key in data) {
            if (data.hasOwnProperty(key)) {
                // 合并其他配置项，支持二级对象
                if (typeof data[key] !== 'object' || data[key] instanceof RegExp || data[key] instanceof Array) {
                    this.userConfig[key] = data[key];
                } else {
                    this.userConfig[key] = extend(this.userConfig[key], data[key]);
                }
            }
        }

        const { devMode } = this.userConfig;

        // 用户传入uid
        if (data.uid) {
            try {
                // 创建加密对象实例
                const encryptor = new JSEncrypt();
                // 设置公钥
                encryptor.setPublicKey(pubPrivateKey);
                // 对uid进行RSA加密
                const userId = encryptor.encrypt(String(data.uid));
                this.userConfig.uid = userId;
                // 将uid设置到cookie的一级域名下，有效时间3年
                setCookie(BLM_UID, userId, THREE_YEARS_IN_MINUTES);
            } catch(error) {
                // 上报RSA加密失败报错
                errManager.reportSystemError(error);
            }
        }
        // 更新配置项
        this.update();

        if (devMode && data.uid) {
            // 线下环境，检测当前用户是否在白名单
            validateUserWhite(this);
        }
    }
    // 获取配置
    get(key) {
        if (key) {
            return this.config[key] || '';
        } else {
            return this.config;
        }
    }
    // 获取上报地址
    getApiPath(key) {
        const appnm = this.get('appnm');
        const business = appnm === 'cp-admin' ? appnm : 'common'
        let path = this.apiPaths[business][key]
        this.url = ONLINE_URL[business]; // 默认上报线上环境
        return `${this.url}${path}`;
    }
    // 更新配置项
    update() {
        // 只更新两级对象
        this.config = this.sourceConfig
        for (let key in this.userConfig) {
            let data = this.userConfig[key]
            if (typeof data != 'object' || data instanceof RegExp || data instanceof Array) {
                this.config[key] = data
            } else {
                // 合并配置项
                this.config[key] = extend(this.config[key], this.userConfig[key])
            }
        }
    }
    // 更新上报域名
    updateReportDomain(reportUrl) {
        this.url = reportUrl;
    }
    // 获取上报域名
    getReportDomain() {
        return this.url;
    }
}

export default ConfigManager;

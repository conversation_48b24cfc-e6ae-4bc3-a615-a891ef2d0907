import Ajax from './xhr';
import Logger from './utils/logger';
import { isFunc } from './utils/type';
import { base64Encode, now } from './utils';
import { addIndexedDB, startPoll } from './cacheRetry';

// 接口请求，超时时间
const NETWORK_TIMEOUT = 5000;

const ua = window.navigator.userAgent;
const isIE = ua.match(/(msie) (\d+)|Trident\/(d+)/i) ? true : false;
const isSafari = !/Chrome/.test(ua) && !isIE && /Mozilla.+AppleWebKit.+Version.+Safari.+/.test(ua);

// 是否支持跨域请求
const isSupportCors = (function () {
    const XHR = window.XMLHttpRequest;
    const hasSupportCors = XHR && ('withCredentials' in (new XHR()));
    return hasSupportCors;
}());

/**
 * 页面性能管理
 *
 * @class EventManager
 */
class NetworkManager {
    constructor(cfgManager) {
        this.cfgManager = cfgManager;
        // 上报数量
        this.reportCount = 0;
    }
    /**
     * URL后面添加随机数
     * @param {String} url 上报地址
     */
    getRndUrl(url) {
        const sendRandom = now().toString(16) + (this.reportCount++);
        return `${url}?rnd=${sendRandom}`;
    }
    /**
     * 报文是否超过URL最大长度限制
     * @param {String} str 上报报文
     */
    isExceedMaxUrlLen(str) {
        let byteLen = str.length;

        for (let i = 0; i < byteLen; i++) {
            let code = str.charCodeAt(i);
            if (code > 0x7F) {
                // 非 ascii 字符算两个字节
                byteLen++;
            }
        }

        // URL 长度是否为 GET 安全长度
        let maxLen = 2024;
        // 保守起见，仅在chrome上放开长度
        if(!/trident/i.test(ua) && !/msie/i.test(ua) && /mozilla.+webkit.+chrome.+/i.test(ua)){
            maxLen = 6000;
        }
        return byteLen * 1.5 > maxLen;
    }
    /**
     * 使用http的上报方式埋点
     * @param {Object} data 埋点信息
     */
    reportWithHTTP(data) {
        // 是否使用GET方式上报
        const shouldUseGetMethod = !isSafari;

        // POST请求地址
        const postUrl = this.cfgManager.getApiPath('post');

        if (shouldUseGetMethod) {
            // 使用GET方式上报埋点
            this.sendWithGetMethod(data);
        } else if (isSupportCors) {
            // 使用POST方式上报埋点
            this.sendWithPostMethod(postUrl, data);
        } else if (isIE && window.XDomainRequest) {
            // IE浏览器下，使用XDomainRequest 发送数据
            this.sendWithIEXDR(postUrl, data);
        } else if (window.navigator.sendBeacon) {
            // 使用sendBeacon方式上报埋点
            this.sendWithNavBeacon(postUrl, data);
        }
    }
    /**
     * 使用GET方式上报埋点
     * @param {Object} data 埋点信息
     */
    sendWithGetMethod(data) {
        const sendData = JSON.stringify(data);

        // POST请求地址
        const postUrl = this.cfgManager.getApiPath('post');

        // 超过埋点请求报文长度限制，转为POST请求上报埋点
        if (this.isExceedMaxUrlLen(sendData)) {
            this.sendWithPostMethod(postUrl, data);
            return;
        }

        let base64DataStr = base64Encode(sendData);
        const sendRandom = now().toString(16) + (this.reportCount++);
        // GET请求地址
        const url = `${this.cfgManager.getApiPath('get')}?c=${encodeURIComponent(base64DataStr)}&rnd=${sendRandom}`;

        const that = this;

        this.sendWithCorsAjax(url, null, {
            method: 'GET',
            cb(code) {
                if (code === 1) {
                    return;
                }

                // 使用sendBeacon方式重试
                that.sendWithNavBeacon(postUrl, data);
                // 将请求失败的埋点缓存到indexedb
                addIndexedDB(data, 'get');
                // 轮训重试
                startPoll(data);
            }
        });
    }
    /**
     * POST方式上报埋点
     * @param {string} postUrl 上报地址
     * @param {string} data 上报的数据
     * @returns {object} opts
     */
    sendWithPostMethod(postUrl, data) {
        // 不支持跨域请求
        if (!isSupportCors) {
            return false;
        }

        const that = this;
        const sendData = JSON.stringify(data);
        this.sendWithCorsAjax(postUrl, sendData, {
            method: 'POST',
            cb(code) {
                if (code === 1) {
                    return;
                }

                // 使用sendBeacon方式重试
                that.sendWithNavBeacon(postUrl, data);
                // 将请求失败的埋点缓存到indexedb
                addIndexedDB(data, 'post');
                // 轮训重试
                startPoll(data);
            }
        });
    }
    /**
     * 使用 IE 的 XDomainRequest 发送数据
     * @param {string} url 信标url
     * @param {Array} data 信标数据
     * @return {boolean}
     */
    sendWithIEXDR(url, data) {
        if (window.XDomainRequest) {
            try {
                const xdr = new XDomainRequest();
                xdr.open(POST, getRndUrl(url), true);
                xdr.onload = function () {
                };

                const that = this;
                xdr.onerror = xdr.ontimeout = function () {
                    that.sendWithNavBeacon(url, data);
                };
                xdr.timeout = NETWORK_TIMEOUT;
                xdr.send(JSON.stringify(data));
            } catch (error) {
                Logger.ignore(error);
            }
        } else {
            return false;
        }
    }
    /**
     * 使用sendBeacon方式上报埋点
     * @param {String} url 埋点上报地址
     * @param {Object} data 埋点信息
     */
    sendWithNavBeacon(url, data) {
        const xhr = window.navigator.sendBeacon;

        if (xhr) {
            xhr && xhr.call(window.navigator, this.getRndUrl(url), JSON.stringify(data));
        } else {
            // 不支持sendBeacon方式上报
            return false;
        }
    }
    /**
     * 发送跨域的AJAX请求 上报埋点日志
     * @param {String} url 埋点上报地址
     * @param {Object} data 埋点信息
     * @param {object} opts 配置参数
     */
    sendWithCorsAjax(url, data, opts) {
        // 不支持跨域请求
        if (!isSupportCors) {
            return false;
        }

        const method = opts && opts.method || 'GET';

        Ajax({
            type: method,
            url,
            data,
            success: (res) => {
                if (isFunc(opts.cb)) {
                    opts.cb(res.code);
                }
            },
            fail: () => {
                if (isFunc(opts.cb)) {
                    opts.cb(0);
                }
            }
        });
    }
    send(data) {
        // 是否使用web上报
        const isUseWebReport = true;

        if (isUseWebReport) {
            // 使用http的上报方式
            this.reportWithHTTP(data);
        }
    }
};

export default NetworkManager;

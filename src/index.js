import ConfigManager from './config';

import { spiderUA } from './utils';;

import EventManager from './event';
import { catchPageLeave, hookBrowserEvents } from './browserEvents';
import validateStateCheck from './debug';
import validateUserWhite from './white';
import { checkIndexedDB } from './cacheRetry';
import { watchEventTracking, exposurePlugin } from './watcher';

// 允许业务设置的公参KEY
const COM_PARAM_KEYS = ['uid', 'channel', 'tenantId', 'envVar', 'devMode', 'category', 'appnm'];

/**
 * 监控类
 */
class Analysis {
    constructor(config, opts) {
        // 实例化配置
        const cfgManager = new ConfigManager(config);
        // 页面上报管理
        this.eventManager = new EventManager(cfgManager);

        // 是否已初始化
        this.isStarted = false;

        this.cfgManager = cfgManager;
    }
    /**
     * 上报PV埋点
     * @param {object} opts
     */
    pageView(opts = {}) {
        const { pageId, eventId, ext = {} } = opts;
        this.eventManager.pageView({ type: 'pv', firstPV: true, pageId, eventId, ext });
    }
    /**
     * 上报页面离开埋点
     * 
     * @param {object} opts
     */
    pageLeave(opts = {}) {
        const { eventId, pageId, ext = {} } = opts || {};
        this.eventManager.pageLeave({ type: 'pl', pageId, eventId, ext });
    }
    /**
     * 上报模块点击埋点
     * 
     * @param {object} opts 
     */
    moduleClick(opts = {}) {
        const { eventId, pageId, ext = {} } = opts;
        this.eventManager.moduleClick({ type: 'click', pageId, eventId, ext });
    }
    /**
     * 上报模块曝光埋点
     * 
     * @param {object} opts
     */
    moduleExposure(opts = {}) {
        const { eventId, pageId, ext = {} } = opts;
        this.eventManager.moduleExposure({ type: 'exposure', pageId, eventId, ext });
    }
    /**
     * API注册曝光元素
     * @param {element} ele 需要采集曝光事件的元素
     * @param {Object} config 埋点配置
     */
    addModuleExposure(ele, opts = {}) {
        exposurePlugin.addModuleExposure(ele, opts);
    }
    /**
     * 注销曝光元素
     * @param {element} ele 需要注销的曝光事件的元素
     */
    removeModuleExposure(ele) {
        exposurePlugin.removeModuleExposure(ele);
    }
    /**
     * 配置埋点公参
     * @param {String} key 公参key
     * @param {String} value 公参value
     */
    set(key, value) {
        // 非公参不允许设置
        if (COM_PARAM_KEYS.indexOf(key) === -1) {
            console.warn('禁止设置该公参');
            return;
        }
        this.cfgManager.set({ [key]: value}, this.errManager);
    }
};

let analysis = new Analysis();

// 支持多个项目和应用的实例的上报
analysis.createCase = Analysis;

/**
 * 启动监控上报
 * 
 * @param {object} configs - 相关配置信息
 */
analysis.start = function(configs) {
    const userAgent =  window.navigator.userAgent;

    spiderUA.forEach(function(item){
        if (userAgent.indexOf(item) !== -1) {
            console.warn('当前环境被判断为爬虫，监控功能关闭');
            return;
        }
    });

    // 只初始化一次
    if (this.isStarted) { 
        return;
    }

    this.isStarted = true;

    if (configs) {
        // 传入用户初始化配置
        this.cfgManager.set(configs);
    }

    // 检测当前用户是否在白名单
    validateUserWhite(this.cfgManager);

    // 检测是否处于埋点验证模式    
    validateStateCheck(this.cfgManager);

    // 监听页面离开
    catchPageLeave(this.eventManager);

    // 监听浏览器事件
    hookBrowserEvents(this.eventManager);

    // 监听声明式埋点
    watchEventTracking(this.eventManager);

    checkIndexedDB({
        appnm: this.cfgManager.get('appnm')
    });
};

export default analysis;

import { BLM_DID, BLM_TID, BLM_COOKIE_S, OFFLINE_URL, ONLINE_URL } from '../constant';
import Logger from './logger';
import { isFunc, isArray, isNumber } from './type';
import db from './db';

// 爬虫的UA标识
export const spiderUA = [
    'Baiduspider',
    'googlebot',
    '360Spider',
    'haosouspider',
    'YoudaoBot',
    'Sogou News Spider',
    'Yisouspider',
    'Googlebot'
];

// 设备id
let deviceId = '';

// 设备id过期时间，默认3年
const THREE_YEARS_IN_MINUTES = 3 * 365 * 24 * 60;

// misid有效时间，默认30分钟
const HALF_HOUR_IN_MINUTES = 30;

// session分隔符
const SESSION_SEPARATOR = '|';

/**
 * 事件绑定
 * @param {document} el 绑定的对象
 * @param {string} name 绑定的事件名
 * @returns {function} handleFunc 绑定的事件函数
 */
export const addEvent = (el, name, handleFunc) => {
    if (el.addEventListener) {
        el.addEventListener(name, handleFunc, false);
    } else if (el.attachEvent) {
        el.attachEvent('on' + name, handleFunc);
    }
};

/**
 * 继承方法
 *
 * @param {object} target 原对象
 * @param {object} source 继承对象
 * @returns {object} 继承后的对象
 */
export const extend = (target, source) => {
    let ret = {}
    let key
    for (key in target) {
        ret[key] = target[key]
    }
    for (key in source) {
        if (source.hasOwnProperty(key) && source[key] !== undefined) {
            ret[key] = source[key]
        }
    }
    return ret
}

/**
 * 对userAgent 解析 返回系统，系统版本，是否端内
 *
 * @param {object} target 原对象
 * @param {object} source 继承对象
 * @returns {object} 继承后的对象
 */
export const parseUA = (UA) => {
    UA = UA || navigator.userAgent;

    let osVersion = '';
    if (UA.indexOf('Mac OS X') > -1) {
        //ios
        var regStr_saf = /OS [\d._]*/gi;
        var verinfo = UA.match(regStr_saf);
        osVersion = (verinfo + "").replace(/[^0-9|_.]/ig, '').replace(/_/ig, '.');
    } else if (UA.indexOf('Android') > -1 || UA.indexOf('Linux') > -1) {
        //android
        osVersion = UA.substr(UA.indexOf('Android') + 8, UA.indexOf(";", UA.indexOf("Android")) - UA.indexOf('Android') - 8);
    } else {
        osVersion = 'unknown';
    }

    return {
        isIOS: !!UA.match(/iOS|iPad|iPhone/i),
        isAndroid: !!UA.match(/Android/i),
        isMobile: !!UA.match(/iOS|iPad|iPhone|Android|windows Phone/i),
        isQQ: !!UA.match(/qq/i),
        isWeixin: !!UA.match(/micromessenger/i),
        isWeibo: !!UA.match(/weibo/i),
        isWindows: !!UA.match(/windows|win32/i),
        isMac: !!UA.match(/macintosh|mac os x/i),
        osVersion,
        isInnerYYApp: !!UA.match(/YYApp/i)
    };
};

/**
 * 获取localStorage
 * @param {string} name 要获取的localStorage的name
 * @return {string} 要获取的localStorage的value
 */
export const getSessionStorageItem = (name) => {
    let result;
    try {
        result = sessionStorage.getItem(name);
        if (result) {
            result = JSON.parse(result);
        }
    } catch (e) {
        Logger.ignore(e);
    }
    return result;
};

/**
 * 设置sessionStorage
 * @param {string} name 要设置的sessionStorage的name
 * @param {string} _data 要设置的sessionStorage的value
 */
export const setSessionStorageItem = (name, _data) => {
    try {
        const data = JSON.stringify(_data);
        sessionStorage.setItem(name, data);
    } catch (e) {
        Logger.ignore(e);
    }
};

/**
 * 设置cookie
 * @param {string} cookieName cookie key
 * @param {string} value cookie value
 * @param {string} minutes cookie有效时间
 * @param {string} domain 设置cookie的域
 */
export const setCookie = (cookieName, value, minutes, domain) => {
    domain = domain || document.domain;
    let date, micoSec, end;
    let cookieStr = cookieName + '=' + encodeURIComponent(value) + ';path=/;domain=' + domain;
    if (minutes !== undefined && !isNaN(minutes)) {
        date = new Date();
        micoSec = parseInt(minutes, 10) * 60 * 1000;
        end = date.getTime() + micoSec;
        date.setTime(end);
        cookieStr += (';expires=' + date.toUTCString());
    }
    try {
        document.cookie = cookieStr;
    } catch (error) {
        Logger.ignore(error)
    }
};

/**
 * 将cookie设置到当前顶级域名下
 * @param {string} cookieName cookie key
 * @param {string} val cookie value
 * @param {string} minutes cookie有效时间
 */
export const setTopCookie = (cookieName, val, minutes) => {
    let domain = document.domain;
    let tmp = domain.split('.');
    let end = tmp.length;
    // 从最后开始，算上localhost
    let start = end - 1;
    let currentVal = ''; 
    let isSuccess = false;
    val = '' + val;

    // 将utm cookie设置到顶级域名
    while (!isSuccess && start >= 0) {
        domain = tmp.slice(start, end).join('.');
        setCookie(cookieName, val, minutes, domain);
        currentVal = getCookie(cookieName);
        if (currentVal !== undefined) {
            isSuccess = currentVal === val;
        }
        start--;
        if (isSuccess) {
            break;
        }
    }
};

/**
 * 获取cookie值
 * 
 * @param {string} name
 */
export const getCookie = (name) => {
    let matches = document.cookie.match(new RegExp('(?:^|;)\\s*' + name + '=([^;]+)'));
    return decodeURIComponent(matches ? matches[1] : '');
}

/**
 * 生成设备id
 * @returns {string} 设备id
 */
export const genDeviceId = (function DID() {
    const userAgent =  window.navigator.userAgent;
    // Time/ticks information
    // 1*new Date() is a cross browser version of Date.now()
    let T = () => {
        let d = 1 * new Date();
        let i = 0;

        // this while loop figures how many browser ticks go by
        // before 1*new Date() returns a new number, ie the amount
        // of ticks that go by per millisecond
        while (d === 1 * new Date() && i < 200) {
            i++;
        }

        return d.toString(16) + i.toString(16);
    };

    // Math.Random entropy
    let R = () => {
        return Math.random().toString(16).replace('.', '');
    };

    // User agent entropy
    // This function takes the user agent string, and then xors
    // together each sequence of 8 bytes.  This produces a final
    // sequence of 8 bytes which it returns as hex.
    let UA = () => {
        let i;
        let ch;
        let buffer = [];
        let ret = 0;

        function xor (result, byteArray) {
            let j, tmp = 0;
            for (j = 0; j < byteArray.length; j++) {
                tmp |= (buffer[j] << j * 8);
            }
            return result ^ tmp;
        }

        for (i = 0; i < userAgent.length; i++) {
            ch = userAgent.charCodeAt(i);
            buffer.unshift(ch & 0xFF);
            if (buffer.length >= 4) {
                ret = xor(ret, buffer);
                buffer = [];
            }
        }

        if (buffer.length > 0) {
            ret = xor(ret, buffer);
        }

        return ret.toString(16);
    };

    return function () {
        let se = (screen.height * screen.width).toString(16);
        return (R() + '-' + T() + '-' + UA() + '-' + se + '-' + T());
    };
})();

/**
 * 获取当时时间戳
 * @return {number}
 */
export const now = () => {
    return +new Date;
};

/**
 * 生成随机数
 * @return {number}
 */
export const rnd = () => {
    return Math.random();
};


/**
 * 生成页面串联标识
 * @private
 * @return {string}
 */
export const generatTraceId = () => {
    return now().toString(16) + '-' + Math.floor(rnd() * 66535) + '-' + Math.floor(rnd() * 66535);
};

/**
 * 获取容器串联标识
 * @private
 * @return {string}
 */
export const getTraceId = () => {
    const traceId = getSessionStorageItem(BLM_TID);

    // 不存在，重新生成
    if (!traceId) {
        const genTraceId = generatTraceId();
        setSessionStorageItem(BLM_TID, genTraceId);
        return genTraceId;
    }

    return traceId;
};

/**
 * 获取页面串联标识
 * @param {boolean} forceUpdate 是否强制更新pageTraceId
 * @return {string}
 */
export const getPageTraceId = (forceUpdate = false) => {
    const genTraceId = generatTraceId();
    return genTraceId;
};

/**
 * 获取设备id
 * @returns {string} 设备id
 */
export const getDeviceId = () => {
    let deviceIdMap = {
        mem: deviceId, // 本地存储的设备id
        cookie: getCookie(BLM_DID), // 从cookie里获取设备id
        ls: db.get(BLM_DID) // 从localStorage里获取设备id
    };

    let did;
    for(let key in deviceIdMap){
        // 只要从以上任一方式里能取到设备id，就直接赋值
        if(deviceIdMap[key]){
            did = deviceIdMap[key];
        }
    }

    // 取不到did，就重新生成设备id
    if(!did){
        did = genDeviceId();
    }

    for(let key in deviceIdMap){
        if(!deviceIdMap[key]){
            switch(key){
                // 本地不存在设备id，重新存储设备id
                case 'mem': 
                    deviceId = did;
                    break;
                // cookie不存在设备id，重新存储设备id
                case 'cookie': 
                    setTopCookie(BLM_DID, did, THREE_YEARS_IN_MINUTES);
                    break;
                // localStorage不存在设备id，重新存储设备id
                case 'ls': 
                    db.add(BLM_DID, did);
                    break;
            }
        }
    }

    return did;
};

const rndSeed = () => {
    const seed = Math.floor(1 + rnd() * 65636);
    return seed.toString(16).substring(1);
};

/**
 * 生成msid
 * @returns {string} msid 28位随机串
 * @private
 */
export function generateMSID () {
    let seeds = [];
    let time = +new Date;
    seeds.push(time.toString(16));
    seeds.push(rndSeed());
    seeds.push(rndSeed());
    seeds.push(rndSeed());
    return seeds.join('-');
};

/**
 * 获取session
 * @param {number} index  0：misid，1：seq上报序号
 * @returns {string} session
 */
export const getSession = (index) => {
    let session = getCookie(BLM_COOKIE_S);

    // 不存在，重新生成misid
    if (!session) {
        session = [ generateMSID(), SESSION_SEPARATOR, '0' ].join('');
        setCookie(BLM_COOKIE_S, session, HALF_HOUR_IN_MINUTES);
    }

    return isNumber(index) ? session.split(SESSION_SEPARATOR)[index] : session.split(SESSION_SEPARATOR);
};

/**
 * 从cookie里获取seq
 * @returns {string} seq 上报序号
 */
const getSeqCookie = () => {
    let seq = 0;
    const seqStr = getSession(1);
    if (!isNaN(seqStr)) {
        seq = parseInt(seqStr);
    }
    return seq < 0 ? 0 : seq;
};

/**
 * 将 msid seq 转换为 session 格式
 * @param {number} seq 当前session发送的数据序列
 * @returns {string}
 * @private
 */
export const convertToSessionFormat = function (seq) {
    const session = [];
    const oldSession = getSession(null);
    session.push(oldSession[0]);
    session.push(isNumber(seq) ? seq : oldSession[1]);
    return session.join(SESSION_SEPARATOR);
};

/**
 * 获取更新后的上报序号
 * @returns {string} 设备id
 */
export const getAndUpdateSeq = () => {
    let seq = getSeqCookie();
    seq = seq || 0;
    seq++;

    const sessionVal = convertToSessionFormat(seq);
    setCookie(BLM_COOKIE_S, sessionVal, HALF_HOUR_IN_MINUTES);
    return seq;
};

/**
 * utf8编码
 * @param {string} 要编码的字符
 * @returns {string} 编码后的字符
 */
export function utf8Encode(string) {
    string = (string + '').replace(/\r\n/g, '\n').replace(/\r/g, '\n');

    let utftext = '',
        start,
        end;
    let stringl = 0,
        n;

    start = end = 0;
    stringl = string.length;

    for (n = 0; n < stringl; n++) {
        let c1 = string.charCodeAt(n);
        let enc = null;

        if (c1 < 128) {
            end++;
        } else if ((c1 > 127) && (c1 < 2048)) {
            enc = String.fromCharCode((c1 >> 6) | 192, (c1 & 63) | 128);
        } else {
            enc = String.fromCharCode((c1 >> 12) | 224, ((c1 >> 6) & 63) | 128, (c1 & 63) | 128);
        }
        if (enc !== null) {
            if (end > start) {
                utftext += string.substring(start, end);
            }
            utftext += enc;
            start = end = n + 1;
        }
    }

    if (end > start) {
        utftext += string.substring(start, string.length);
    }

    return utftext;
}


const _base64Encode = (data) => {
    let b64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
    let o1, o2, o3, h1, h2, h3, h4, bits, i = 0,
        ac = 0,
        enc = '',
        tmp_arr = [];

    if (!data) {
        return data;
    }

    data = utf8Encode(data);

    do { // pack three octets into four hexets
        o1 = data.charCodeAt(i++);
        o2 = data.charCodeAt(i++);
        o3 = data.charCodeAt(i++);

        bits = o1 << 16 | o2 << 8 | o3;

        h1 = bits >> 18 & 0x3f;
        h2 = bits >> 12 & 0x3f;
        h3 = bits >> 6 & 0x3f;
        h4 = bits & 0x3f;

        // use hexets to index into b64, and append result to encoded string
        tmp_arr[ac++] = b64.charAt(h1) + b64.charAt(h2) + b64.charAt(h3) + b64.charAt(h4);
    } while (i < data.length);

    enc = tmp_arr.join('');

    switch (data.length % 3) {
        case 1:
            enc = enc.slice(0, -2) + '==';
            break;
        case 2:
            enc = enc.slice(0, -1) + '=';
            break;
    }

    return enc;
};

/**
 * base64加密
 * @param {string} 要编码的字符
 * @returns {string} 编码后的字符
 */
export const base64Encode = (data) => {
    let base64Data =  _base64Encode(data);

    const BTOA = window.btoa;
    const ua = navigator.userAgent;

    try{
        if (isFunc(BTOA) && BTOA(utf8Encode(ua)) === _base64Encode(ua)) {
            base64Data = BTOA(utf8Encode(data));
        }
    } catch(error){
        Logger.ignore(error);
    }
    

    return base64Data;
};

/**
 * 当前是否是Native Code函数方法
 * @param {function} fn 函数
 * @returns {boolean} 是否是Native Code函数方法
 */
export const isNativeFunc = (fn) => {
    return isFunc(fn) && /\[native code\]/.test(fn.toString());
};

/**
 * 判断一个属性是定义在对象本身
 * @param {object} obj 对象
 * @param {string|element} prop 对象
 */
export const hasOwnProperty = (obj, prop) =>  {
    return Object.prototype.hasOwnProperty.call(obj, prop);
};

/**
 * 创建宏任务
 */
export const macroTask = (function(){
    if(isFunc(window.setImmediate) && /\[native code\]/.test(window.setImmediate.toString())){
        return function(fn){
            window.setImmediate(fn);
        };
    }else if(isFunc(window.Promise) && /\[native code\]/.test(window.Promise.toString())){
        return function(fn){
            window.Promise.resolve().then(fn);
        };
    }else{
        return function(fn){
            setTimeout(fn);
        };
    }
})();

/**
 * each循环
 * @param {object} arrLikeObj 对象
 * @param {function} func 执行的函数
 * @param {function | object} thisArg 对象指向
 */
export const each = (arrLikeObj, func, thisArg) => {
    if (!arrLikeObj) {
        return;
    }

    if (isArray(arrLikeObj)) {
        for (let i = 0; i < arrLikeObj.length; i++) {
            const ret = func.call(thisArg, arrLikeObj[i], i, arrLikeObj);
            if (ret === false) {
                break;
            }
        }
    } else {
        for (let p in arrLikeObj) {
            if (hasOwnProperty(arrLikeObj, p)) {
                const ret = func.call(thisArg, arrLikeObj[p], p, arrLikeObj);
                if (ret === false) {
                    break;
                }
            }
        }
    }
};

/**
 * DOM加载完成调用方法
 * @param {function} fn 执行的函数
 */
export const bindReady = (fn, win) => {
    win = win || window;
    var done = false,
        top = true,
        doc = win.document,
        root = doc.documentElement,
        modern = doc.addEventListener,
        add = modern ? 'addEventListener' : 'attachEvent',
        rem = modern ? 'removeEventListener' : 'detachEvent',
        pre = modern ? '' : 'on';

        const init = (e) => {
            if (e.type == 'readystatechange' && doc.readyState != 'complete') {
                return;
            }
    
            (e.type == 'load' ? win : doc)[rem](pre + e.type, init, false);

            if (!done && (done = true)) {
                fn.call(win, e.type || e);
            }
        };

        const poll = () => {
            try {
                root.doScroll('left');
            } catch (e) {
                setTimeout(poll, 50);
                return;
            }
            init('poll');
        };

    if (doc.readyState == 'complete') {
        fn.call(win, 'lazy');
    } else {
        if (!modern && root.doScroll) {
            try {
                top = !win.frameElement;
            } catch (e) {
                logger.log(e);
            }

            if (top) {
                poll();
            }
        }
        doc[add](pre + 'DOMContentLoaded', init, false);
        doc[add](pre + 'readystatechange', init, false);
        win[add](pre + 'load', init, false);
    }
};

/**
 * 单页面路由事件监听
 * @param {function} callback 执行的函数
 */
export const addSinglePageEvent = (callback) => {
    let currentUrl = location.href;
    const historyPushState = window.history.pushState;
    const historyReplaceState = window.history.replaceState;

    if (isFunc(window.history.pushState)) {
        window.history.pushState = function() {
            historyPushState.apply(window.history, arguments);
            callback(currentUrl);
            currentUrl = location.href;
        };
    }

    if (isFunc(window.history.replaceState)) {
        window.history.replaceState = function() {
            historyReplaceState.apply(window.history, arguments);
            callback(currentUrl);
            currentUrl = location.href;
        };
    }

    let singlePageEvent = null;
    // IE浏览器 默认使用hashchange监听
    if (window.document.documentMode) {
        singlePageEvent = 'hashchange';
    } else {
        singlePageEvent = historyPushState ? 'popstate' : 'hashchange';
    }

    addEvent(window, singlePageEvent, function() {
        callback(currentUrl);
        currentUrl = location.href;
    });
};


 export const pointInterFaceUrl = (appnm = '') => {
    return `/${appnm === 'cp-admin' ? 'admin' : 'h5'}`
 }

const global = {
    getInstance: function(){
        return window
    },
    getConstructor: function(){
        return Window
    },
    getOrigin: function(){
        let origin = window.location.origin
        if(!origin){
            origin = window.location.protocol + '//' + window.location.hostname + (window.location.port ? ':' + window.location.port: '');
        }
        return origin
    },
    getProtocol: function(){
        let protocol = window.location.protocol
        return protocol
    },
    getHref: function(){
        let href = window.location.href
        return href
    },
    getSearch: function(){
        let search = location.search
        return search;
    },
    getUserAgent: function(){
        return window.navigator.userAgent
    }
}

export default global;
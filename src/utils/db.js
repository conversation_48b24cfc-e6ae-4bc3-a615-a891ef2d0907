const LS_KEY = 'blm_monitor_cache';
import Logger from './logger';

export default {
    // 获取LocalStorage存储
    get: function(key = LS_KEY) {
        let cache = null;
        try {
            let cacheStr = localStorage.getItem(key)
            if (cacheStr) {
                cache = JSON.parse(cacheStr)
            }
        } catch (e) {
            Logger.ignore(e)
        }
        return cache
    },
    // 添加LocalStorage存储
    add: function(key = LS_KEY, nextCache) {
        if (nextCache instanceof Array) {
            let preCache = this.get()
            nextCache = nextCache.concat(preCache)
            try {
                localStorage.setItem(key, JSON.stringify(nextCache))
            } catch (e) {
                Logger.ignore(e)
            }
        }
    },
    // 删除LocalStorage存储
    clear: function(key = LS_KEY) {
        try {
            localStorage.removeItem(key)
        } catch (e) {
            Logger.ignore(e)
        }
    }
}
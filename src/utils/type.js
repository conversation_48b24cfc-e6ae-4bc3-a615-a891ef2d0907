const toStr = Object.prototype.toString;

export const _typeof = (obj) => {
    return (typeof obj);
}

export function isType(obj, type) {
    return _typeof(obj) === type;
}

export function isStr(obj) {
    return isType(obj, 'string');
}

export function isFunc(obj){
    return isType(obj, 'function');
}

export function isNumber(obj){
    return isType(obj, 'number');
}

export function isObj(obj) {
    return obj && '[object Object]' === toStr.call(obj);
}

export function isBoolean(obj) {
    return isType(obj, 'boolean');
}

export function isArray(arr) {
    return arr && '[object Array]' === toStr.call(arr);
}

export function isNull(obj) {
    return !obj && isType(obj, 'object');
}

export function isElement(arg) {
    return !!(arg && arg.nodeType === 1);
}
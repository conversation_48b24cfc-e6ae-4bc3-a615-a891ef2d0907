import { each, bindReady, extend, addEvent } from './utils';
import { isStr, isObj, isElement } from './utils/type';

const BLM_EXP = 'blm-exposure';
const BLM_CLICK = 'blm-click';

// 曝光队列
let expMap = [];

// 曝光监听对象
let expIntersection = {};

// 事件实例
let EventManager = null;

// 曝光默认配置
const expConfig = {
    rate: 0.5,      // 曝光比例。默认: 0，值域: 0~1
    delay: 0,       // 曝光有效停留时长，单位为秒
    repeated: true, // 支持曝光的重复上报
};

const isSupport = () => {
    if (('MutationObserver' in window || 'WebKitMutationObserver' in window || 'MozMutationObserver' in window) && 'IntersectionObserver' in window) {
        return true;
    }
    return false;
};

export const exposurePlugin =  {
    isStarted: false,   // 是否已初始化
    init: function () {
        if (!isSupport()) {
            console.log('当前浏览器不支持声明式埋点');
            return;
        }

        const that = this;

        bindReady(function() {
            // dom 加载完毕后，检测当前元素列表，包含的曝光元素
            const nodes = that.getElesByEventName();
            that.addObserveByNodes(nodes);
            // 注册 document.body 变动监听
            exposureMutation.init();
        });

        this.isStarted = true;
    },
    /**
     * 根据记录，启动曝光监听
     */
    start: function() {
        each(expMap, function (option) {
            const { config, ele } = option;
            const intersection = expIntersection[config.rate];

            // 监听实例不存在 || 不是标签元素
            if (!intersection || !isElement(ele)) {
                return;
            }

            intersection.observe(ele);
        });
    },
    /**
     * 根据记录，暂停曝光可视区的监听并移除待曝光事件发送
     */
    stop: function () {
        each(expMap, function (option) {
            const { config, ele } = option;
            const intersection = expIntersection[config.rate];

            // 监听实例不存在 || 不是标签元素
            if (!intersection || !isElement(ele)) {
                return;
            }
            
            // 取消DOM监听
            intersection.unobserve(ele);

            // 关闭定时器
            if (option.timer) {
                clearTimeout(option.timer);
                option.timer = null;
            }
        });
    },
    /**
     * 清空上一页的声明式埋点，并监测当前页面的曝光元素
     */
    clear: function () {
        this.stop();
        expIntersection = {};
        expMap = [];

        // 检测当前页面的曝光元素
        const nodes = this.getElesByEventName();
        this.addObserveByNodes(nodes);
    },
    /**
     * 移除单一元素视口监听及移除该元素延时曝光事件
     * @param {Element} ele dom元素
     */
    removeWatchEle: function(ele) {
        let eleOption = null;
        let index = -1;

        each(expMap, function(item, ind) {
            if (ele === item.ele) {
                eleOption = item;
                index = ind;
            }
        });

        if (!eleOption) {
            return;
        }

        const { config } = eleOption;
        const intersection = expIntersection[config.rate];

        // dom没有被监听 || 不是dom元素
        if (!intersection || !isElement(ele)) {
            return;
        }

        // 解除监听
        intersection.unobserve(ele);

        if (eleOption.timer) {
            clearTimeout(eleOption.timer);
            eleOption.timer = null;
        }

        if (index > -1) {
            // 从曝光队列删除元素
            expMap.splice(index, 1);
        }
    },
    /**
     * 获取所有字元素
     * @param {Object} domList dom元素
     */
    getElesByEventName: function(el) {
        const ele = el || document;
        return ele.querySelectorAll(`[${BLM_EXP}]`);
    },
    /**
     * 根据列表查找曝光元素并注册曝光监听
     * @param {Object} nodes node节点
     */
    addObserveByNodes: function(nodes) {
        if (nodes && nodes.length) {
            each(nodes, function(node) {

                if (node.nodeType === 1 && node.hasAttribute(BLM_EXP)) {
                    const eleAttrOption = {};
                    let data = null;

                    try{
                        // 解析埋点属性
                        data = JSON.parse(node.getAttribute(BLM_EXP));
                    } catch(e){
                        console.error(`节点 :`, node, `的blm-exposure值不是标准的JSON格式：`, node.getAttribute(BLM_EXP));
                        return;
                    }

                    if (data) {
                        eleAttrOption.config = extend(expConfig, data);
                        eleAttrOption.ele = node;

                        // 添加元素视图监听
                        exposurePlugin.addOrUpdateWatchEle(eleAttrOption);
                    }
                }
            });
        }
    },
    /**
     * 效验埋点参数
     * @param {object} opts
     */
    validationParams: function(opts = {}) {
        let isValidate = true;

        const { pageId, eventId } = opts;

        if (!eventId) {
            Logger.ignore('eventId不得为空');
            isValidate = false;
        }

        if (!isStr(pageId)) {
            Logger.ignore('传入的eventId必须是字符类型');
            isValidate = false;
        }

        if (!pageId) {
            Logger.ignore('pageId不得为空');
            isValidate = false;
        }

        if (!isStr(pageId)) {
            Logger.ignore('传入的pageId必须是字符类型');
            isValidate = false;
        }

        return isValidate; 
    },
    /**
     * 获取视口监听对象，如未注册相关配置的视口监听，则创建。如已注册则根据配置直接返回
     * @param {Object} config
     */
    getIntersection: function(config) {
        const { rate } = config;
        let intersection = null;
        const that = this;

        if (expIntersection[rate]) {
            intersection = expIntersection[rate];
        } else {
            intersection = expIntersection[rate] = new IntersectionObserver(
                function() {
                    that.exposure.apply(that, arguments);
                },
                {
                    threshold: rate
                }
            );
        }

        return intersection;
    },
    /**
     * 添加元素视图监听
     * @param {Object} option
     */
    addOrUpdateWatchEle: function(option) {
        const { ele, config } = option;

        // 效验埋点参数
        if (!this.validationParams(config)) {
            return;
        }


        let eleOption = this.getEleOption(ele);

        if (eleOption) {
            eleOption = extend(eleOption, option);
            // 如已发送且允许重复发送则置为 未发送
            if (eleOption.config.repeated) {
                eleOption.config.isSend = false;
            }
        } else {
            // 元素未被监听, 进行监听
            const intersection = this.getIntersection(config);
            intersection.observe(ele);
            expMap.push(option);
        }
    },
    /**
     * 获取某个元的曝光属性及配置
     * @param {Element} ele
     */
    getEleOption: function(ele) {
        let eleOption = null;

        each(expMap, function(option) {
            if (ele === option.ele) {
                eleOption = option;
            }
        });

        return eleOption;
    },
    /**
     * 元素进入可视区域，发送曝光事件
     * @param {Array} entries
     */
    exposure: function(entries) {
        const that = this;

        each(entries, function(entry) {
            const ele = entry.target;
            const eleOption = that.getEleOption(ele);


            if (entry.isIntersecting === false || !eleOption || eleOption.config.isSend) {
                if (eleOption && eleOption.timer) {
                    clearTimeout(eleOption.timer);
                    eleOption.timer = null;
                }
                return;
            }

            // 目标元素的可见比例 大于默认值，代表曝光元素进入可视区域
            if (entry.intersectionRatio >= eleOption.config.rate) {
                if (eleOption.timer) {
                    // 清空定时器
                    clearTimeout(eleOption.timer);
                    eleOption.timer = null;
                }

                // 曝光延迟时间，单位毫秒
                const delay = eleOption.delay * 1000;

                eleOption.timer = setTimeout(function () {
                    const eleRect = ele.getBoundingClientRect();
                    const eleOption = that.getEleOption(ele);
                    const { width, height } = eleRect;

                    // 再次检测
                    if (!width || !height || !eleOption || eleOption.config.isSend) {
                        return;
                    }

                    // 上报曝光事件
                    const { pageId, eventId, ext } = eleOption.config || {};
                    EventManager.moduleExposure({
                        type: 'exposure',
                        pageId,
                        eventId,
                        ext
                    });
                    // 设置为已发送
                    eleOption.config.isSend = true;
                    // 如已发送且允许重复发送则置为 未发送
                    if (eleOption.config.repeated) {
                        eleOption.config.isSend = false;
                    }
                }, delay);
            }
        });
    },
    /**
     * API注册曝光元素
     * @param {element} ele 需要采集曝光事件的元素
     * @param {Object} config 埋点配置
     */
    addModuleExposure(ele, config) {
        // 未初始化完成
        if (!this.isStarted) {
            return;
        }

        const { pageId, eventId, ext = {} } = config;
        
        // 不是dom元素
        if (!isElement(ele)) {
            return;
        }

        // 效验埋点参数
        if (!this.validationParams(config)) {
            return;
        }

        const eleOption = {
            ele: ele,
            config: isObj(config) ? config : {}
        };
        let expEleOption = this.getEleOption(ele);

        if (expEleOption) {
            // 合并配置
            expEleOption = extend(expEleOption, eleOption);
            // 如已发送且允许重复发送则置为 未发送
            if (expEleOption.config.repeated) {
                expEleOption.config.isSend = false;
            }
        } else {
            // 合并配置
            eleOption.config = extend(expConfig, eleOption.config);
            // 添加元素视图监听
            this.addOrUpdateWatchEle(eleOption);
        }
    },
    /**
     * 注销曝光元素
     * @param {element} ele 需要注销的曝光事件的元素
     */
    removeModuleExposure(ele) {
        // 未初始化成功
        if (!this.isStarted) {
            return;
        }

        // 不是dom元素
        if (!isElement(ele)) {
            return;
        }

        this.removeWatchEle(ele);
    }
};

const exposureMutation = {
    init: function() {
        let mutationObserver = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver;
        this.mutationObserver = new mutationObserver(this.listenNodesChange);

        this.mutationObserver.observe(document.body, {
            attributes: true, // 观察目标节点的属性节点
            childList: true,  // 观察目标节点的子节点的新增和删除
            subtree: true,    // // 观察目标节点的所有后代节点
            attributeOldValue: true
        });
    },
    /**
     * 监听DOM变化
     * @param {Object} domList dom元素
     */
    listenNodesChange: function(domList) {
        each(domList, function(el) {
            switch(el.type) {
                // 子元素发生变更
                case 'childList': 
                    // 存在删除的dom节点
                    if (el.removedNodes && el.removedNodes.length) {
                        each(el.removedNodes, function(node) {
                            // 元素节点
                            if (node.nodeType === 1) {
                                // 当前元素
                                exposurePlugin.removeWatchEle(node);
    
                                // 变动元素子元素查找
                                const removeNodes = exposurePlugin.getElesByEventName(node);

                                if (removeNodes.length) {
                                    each(removeNodes, function(removeNode) {
                                        exposurePlugin.removeWatchEle(removeNode);
                                    });
                                }
                            }
                        });            
                    } else if (el.addedNodes && el.addedNodes.length) {
                        // 存在新增的dom节点
                        // 根据列表查找曝光元素并注册曝光监听
                        exposurePlugin.addObserveByNodes(el.addedNodes);

                        // 变动元素子元素查找
                        each(el.addedNodes, function(node) {
                            if (node.nodeType === 1) {
                                const nodes = exposurePlugin.getElesByEventName(node);
                                exposurePlugin.addObserveByNodes(nodes);
                            }
                        });
                    }
                    break;
                // 标签属性发生变更
                case 'attributes': 
                    // 当前元素没有属性
                    if (!el.attributeName) {
                        return false;
                    }

                    const ele = el.target;
                    const changeAttrKey = el.attributeName;
                    // 不属于曝光事件 不进行不监听
                    if (!isStr(changeAttrKey) || changeAttrKey.indexOf(BLM_EXP) === -1) {
                        return;
                    }

                    let data = null;
                    try{
                        // 解析埋点属性
                        data = JSON.parse(ele.getAttribute(BLM_EXP));
                    } catch(e){
                        console.error(`节点 :`, ele, `的lx-mv值不是标准的JSON格式：`, ele.getAttribute(BLM_EXP));
                        return;
                    }

                    if (data) {
                        // 如新增属性，则使用默认配置
                        const eleOption = exposurePlugin.getEleOption(ele) || { ele: ele, config: expConfig };
                        // 合并配置
                        const configs = extend(expConfig, data);
                        const updateEleOption = extend(eleOption, { config: configs });
                        // 先删除原有的队列中的曝光元素，在将修改属性后的元素添加到队列中
                        exposurePlugin.removeWatchEle(ele);
                        exposurePlugin.addOrUpdateWatchEle(updateEleOption);
                    }
            }
        });
    }
};

export const clickPlugin = {
    init: function() {
        const that = this;

        bindReady(function() {
            addEvent(document.body, 'click', function(ev) {
                const targetNode = that.findParentNode(ev.target, function(node){
                    if (that.hasLXAttr(node, BLM_CLICK)) {
                        return node.getAttribute(BLM_CLICK);
                    }
                    return false;
                });

                if (targetNode) {
                    if (that.hasLXAttr(targetNode, BLM_CLICK)) {
                        try {
                            const config = JSON.parse(targetNode.getAttribute(BLM_CLICK));
                            const { pageId, eventId, ext } = config;
                            const opts = {
                                pageId,
                                eventId,
                                ext,
                                type: 'click'
                            };
                            // 触发点击埋点上报
                            EventManager.moduleClick(opts);
                        } catch(error) {
                            console.error(`节点 :`, targetNode, `的blm-click值不是标准的JSON格式：`, targetNode.getAttribute(BLM_CLICK));
                        }
                    }
                }
            });
        });
    },
    /**
     * DOM是否包含blm-click属性
     */
    hasLXAttr: (node, nm) => {
        return node.getAttribute && node.getAttribute(nm);
    },
    /**
     * 注销曝光元素
     * @param {element} ele 需要查找的元素
     * @param {function} fn 执行函数
     */
    findParentNode: (ele, fn) => {
        let pNode = ele;
        if(!pNode || pNode === document.body || !pNode.parentNode){
            return false;
        }
        if(fn(pNode)){
            return pNode;
        }
        return clickPlugin.findParentNode(pNode.parentNode, fn);
    }
};

export const watchEventTracking = (eventManager) => {
    EventManager = eventManager;
    // 初始化曝光元素的监听事件
    exposurePlugin.init();
    // 初始化点击元素的监听事件
    clickPlugin.init();
};
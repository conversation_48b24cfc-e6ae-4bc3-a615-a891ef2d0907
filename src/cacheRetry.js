import Ajax from './xhr';
import { isFunc } from './utils/type';
import { base64Encode, now, pointInterFaceUrl } from './utils';
import logger from './utils/logger';

const ua = window.navigator.userAgent;
const isIE = ua.match(/(msie) (\d+)|Trident\/(d+)/i) ? true : false;
const isFirefox = /firefox/i.test(ua);

// 缓存数据最大数量
const DATA_COUNT_LIMIT = 100;
// 轮训间隔时间，默认6秒
const POLL_TIME_GAP = 1000 * 6;
// 重试停止时间
const RETRY_TIME_LIMIT = 1000 * 60 * 5;

const DB = !isIE && window.indexedDB;
const IDBFactory = window.IDBFactory;

// 当前是否支持indexedDB
const supportDB = DB && isFunc(DB.open) && DB.constructor === IDBFactory;
// 是否连接indexedDB
let connectable = false;
// 是否不启用indexedDB
let unSupportDB = false;
// 是否轮训
let isPolling = false;
let pollStartTime = null;

let db = null;
let store = null;
// 当前缓存的埋点数量
let currentDataCount = 0;

/**
 * 发送缓存埋点
 */
const sendData = (content, info = {}) => {
    const { data, type } = content;
    console.log('cacheRetry: send indexedDB Cache: ', data);

    const sendRandom = now().toString(16);
    if (type === 'get') {
        // get请求，使用XHR方式进行埋点上报
        const url = `${pointInterFaceUrl(info?.appnm)}/v1/common/log/hand/eventV1?c=${encodeURIComponent(base64Encode(data))}&t=cache&rnd=${sendRandom}`;

        Ajax({
            type: 'get',
            url
        });
    } else {
        // post请求，使用sendBeacon方式进行埋点上报
        try {
            window.navigator.sendBeacon(`${pointInterFaceUrl(info?.appnm)}/v1/common/log/hand/eventV2?t=cache&rnd=${sendRandom}`, data);
        } catch (error) {
            console.log('当前不支持sendBeacon');
        }
    }
};

/**
 * 初始化indexedDB
 */
export const initIndexedDB = () => {
    return new Promise((resolve) => {
        if (unSupportDB) {
            // 不启用indexedDB
            return resolve(false);
        }

        if (isFirefox) {
            logger.ignore('firefox内不启用indexedDB');
            unSupportDB = true;
            return resolve(false);
        }

        // 当前不支持indexedDB
        if (!supportDB) {
            unSupportDB = true;
            return resolve(false);
        }

        // 已建立indexedDB
        if (connectable) {
            return resolve(true);
        }

        try {
            // 打开indexedDB数据库
            const request = DB.open('_blmsdk_db', 1000);
            // 成功indexedDB打开数据库
            request.onsuccess = function(ev) {
                console.log('cacheRetry: db open succeed');
                connectable = true;
                db = ev.target.result;
                resolve(true);
            };

            // indexedDB数据库升级事件(新建数据库 或者 指定的版本号，大于数据库的实际版本号就会触发)
            request.onupgradeneeded = function(ev) {
                db = ev.target.result;
                // 新增cache的表格，主键是id, 并且自动增量
                store = db.createObjectStore('cache', {
                    keyPath: 'id',
                    autoIncrement: true // 主键自动增量
                });
                // 新建索引（允许重复的值）
                store.createIndex('data', 'data', { unique: false });
                store.createIndex('type', 'type', { unique: false });
            };
        } catch(error) {
            unSupportDB = true;
            connectable = false;
            return resolve(false);
        }
    });
};

/**
 * 检查并上报indexedDB缓存埋点数据
 * @param {boolean} reportDomain 上报域名
 */
export const checkIndexedDB = (info = {}) => {
    initIndexedDB().then((res) => {
        // 初始化失败
        if (!res) {
            return;
        }

        console.log('cacheRetry: start checking db...');

        // 从indexedDB里拿到之前缓存的数据
        const transaction = db.transaction(['cache'], 'readwrite');
        const store = transaction.objectStore('cache');

        const cursor = store.openCursor();
        const allData = [];

        // 成功打开indexedDB数据库
        cursor.onsuccess = function (event) {
            const result = event.target.result;
            if (result) {
                // 拿出所有indexedDB里缓存的数据
                allData.push(result.value);
                result.continue();
            } else {
                currentDataCount = allData.length;
                console.log(`cacheRetry: sending data...${allData.length} `);
                // 数据全部取完后，进行埋点上报，并从indexedDB里删除之前缓存的数据
                allData.forEach(function (d) {
                    sendData(d, info);
                    store.delete(d.id);
                });
            }
        };
    });
};

/**
 * 将埋点数据缓存到indexedb
 * @param {object} data 需要缓存的埋点数据
 * @param {string} type 上报方式，get、post
 */
export const addIndexedDB = (data, type) => {
    initIndexedDB().then((res) => {
        if (!res) {
            return;
        }

        const transaction = db.transaction(['cache'], 'readwrite');
        const store = transaction.objectStore('cache');

        // 缓存数据超过缓存最大数量，清空indexedDB缓存数据
        if (currentDataCount > DATA_COUNT_LIMIT) {
            console.log('idb clear');
            store.clear();
        }

        console.log(`cacheRetry: add data... `);
        store.add({
            type,
            data: JSON.stringify(data)
        });
    });
};

/**
 * 轮训上报缓存埋点
 */
export const startPoll = (data = {}) => {
    // 停止轮训 || 不支持indexedDB
    if (isPolling || !supportDB) {
        return;
    }

    isPolling = true;
    pollStartTime = +new Date();

    initIndexedDB().then((res) => {
        if (!res) {
            return;
        }
        
        setTimeout(function poll() {
            console.log('cacheRetry: check net status...');

            const id = '_blmsdk_poll_img';
            const imgBeacon = new Image();
            window[id] = imgBeacon;

            imgBeacon.onload = function() {
                checkIndexedDB({
                    appnm: data?.statCommon?.appnm
                });
                isPolling = false;
                console.log('cacheRetry: net status OK, check indexedDB...');
                window[id] = null;
            };

            // 重试上报缓存埋点
            const retryEvent = () => {
                console.log('cacheRetry: net status ERROR, check again after 5s...');
                const nowDate = (new Date()).getTime();
                if (nowDate - pollStartTime > RETRY_TIME_LIMIT) {
                    console.log('cacheRetry: net status ERROR, check overtime, stop');
                    return;
                }
                setTimeout(poll, POLL_TIME_GAP);
                window[id] = null;
            };

            imgBeacon.onabort = retryEvent;
            
            imgBeacon.onerror = retryEvent

            imgBeacon.src = `${pointInterFaceUrl(data?.statCommon?.appnm)}/v1/common/log/hand/heart`;
        }, POLL_TIME_GAP)
    });
};

initIndexedDB();

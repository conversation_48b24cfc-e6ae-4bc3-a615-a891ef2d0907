//  重写xhr open和send方法，监控页面上的ajax调用
const xhr = window.XMLHttpRequest;
import Logger from './utils/logger';
const NOOP = function () {};

/**
 * 重写XMLHttpRequest
 */
const init = function () {
    if (!xhr) {
        return;
    }

    let protocol = window.location.protocol;

    // if (protocol === 'file:') { 
    //     return
    // }

    let _open = xhr.prototype.open;
    let _send = xhr.prototype.send;

    xhr.prototype.open = function (method, url) {
        this.url = url;
        this._startTime = +new Date();
        return _open.apply(this, arguments);
    }

    xhr.prototype.send = function () {

        var ADD_EVENT_LISTENER = 'addEventListener';
        var ON_READY_STATE_CHANGE = 'onreadystatechange';
        var _dispatchEvent = (event) => {
            if (event) {
                var duration = +new Date() - this._startTime;
                event.duration = duration;

                let response;
                try {
                    if (this.getAllResponseHeaders('content-type').indexOf('application/json') !== -1) {
                        response = event.currentTarget.response;
                        response = JSON.parse(response);
                    }
                } catch (e) {
                    Logger.ignore(e);
                }
                if (event.currentTarget.status === 200) {
                    this.success && this.success(response);
                } else {
                    this.fail && this.fail(response);
                }
            }
        }

        if (ADD_EVENT_LISTENER in this) {
            // xhr Level 2
            this[ADD_EVENT_LISTENER]('load', _dispatchEvent);
            this[ADD_EVENT_LISTENER]('error', _dispatchEvent);
            this[ADD_EVENT_LISTENER]('abort', _dispatchEvent);
        } else {
            var _originStateChange = this[ON_READY_STATE_CHANGE];
            this[ON_READY_STATE_CHANGE] = function (event) {
                if (this.readyState === 4) {
                    _dispatchEvent(event);
                }
                if (_originStateChange) {
                    _originStateChange.apply(this, arguments);
                }
            }
        }

        return _send.apply(this, arguments);
    }
}
init();

/**
 * 封装AJAX接口
 */
const Ajax = (opts) => {
    if (!opts) return


    let UA = window.navigator.userAgent
    let browserName = window.navigator.appName

    const isIE89 = browserName.indexOf('Microsoft Internet Explorer') !== -1 &&
        (UA.indexOf('MSIE 8.0') !== -1 || UA.indexOf('MSIE 9.0') !== -1)

    const useXDomainRequest = isIE89 && window.XDomainRequest;

    let req;
    if (useXDomainRequest) {
        req = new XDomainRequest();
    } else {
        req = new XMLHttpRequest();
    }

    req.open(opts.type || 'GET', opts.url, true)
    req.success = opts.success || NOOP
    req.fail = opts.fail || NOOP
    if (opts.type === 'POST') {
        if (opts.header && !useXDomainRequest) {
            for (let key in opts.header) {
                if (opts.header.hasOwnProperty(key)) {
                    req.setRequestHeader(key, opts.header[key])
                }
            }

        }
        req.send(opts.data);
    } else {
        req.send();
    }
    return req;
}

export default Ajax;

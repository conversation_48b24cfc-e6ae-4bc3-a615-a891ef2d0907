import { parseUA, now, addEvent, addSinglePageEvent } from './utils';
import { exposurePlugin } from './watcher'; 

// 记录最近一次visibilitystate切换为hidden的时间
let lastHiddenTime = null;
let lastVisibleTimer = null;
let lastHiddenTimer = null;

// mac下chrome全屏会触发visibilityChange事件，利用同时触发resize事件的特点做一个标记
let isJustResized = false;
let resizeTimer = null;


/**
 * 监测页面离开/关闭
 * @param eventManager
 */
export const catchPageLeave = (eventManager) => {
    const { isMobile, isIOS } = parseUA();

    const beforeunload = () => {
        // 发送PL事件
        eventManager.sendPageLeave();
    };

    if (isMobile && isIOS) {
        // iphone设备，通过pagehide监听页面关闭
        addEvent('pagehide', beforeunload);
    } else {
        // 监听页面关闭
        addEvent('beforeunload', beforeunload);
    }
};

/**
 * 绑定浏览器事件
 * @param eventManager
 */
export const hookBrowserEvents = (eventManager) => {
    // 当前浏览器不支持事件
    if (!'visibilityState' in document || !'onresize' in window) {
        return;
    }

    // 在浏览器hidden状态下
    if (document.visibilityState !== 'visible') {
        lastHiddenTime = now();
    }

    // 最小间隔2s，小于这个间隔不自动发PV和PL
    const vcGap = 2000;

    addEvent(document, 'visibilitychange', function() {
        let state = document.visibilityState;

        if (state === 'visible') {
            lastVisibleTimer = setTimeout(() => {
                // 触发resize事件
                if(isJustResized){
                    return;
                }

                if (document.visibilityState !== 'visible') {
                    return;
                }

                // 自动发送曝光时间
                exposurePlugin.start();

                // 用户切换浏览器tab在回来超过2秒时间，自动发送PV
                if (now() - lastHiddenTime >= vcGap) {
                    clearTimeout(lastHiddenTimer);
                    if (eventManager.getCurrentPageOps()) {
                        // 发送自动PV事件
                        const ops = { ...eventManager.getCurrentPageOps(), isAuto: 1, firstPV: false, type: 'pv' };
                        eventManager.pageView(ops, true);
                    }
                }
            }, 1000);
        } else {
            lastHiddenTime = now();
            lastHiddenTimer = setTimeout(function check() {
                // 触发resize事件
                if(isJustResized){
                    return;
                }

                if (document.visibilityState === 'visible') {
                    return;
                }

                // 关闭曝光事件的监听
                exposurePlugin.stop();

                // 用户切换浏览器tab在回来超过2秒时间，自动发送PV
                if (now() - lastHiddenTime >= vcGap) {
                    clearTimeout(lastVisibleTimer);

                    if (eventManager.getCurrentPageOps()) {
                        // 发送自动PD事件
                        const ops = { ...eventManager.getCurrentPageOps(), isAuto: 1, timeStamp: lastHiddenTime };
                        eventManager.pageLeave(ops);
                    }
                } else {
                    // 1秒轮训，监测上报PL事件
                    lastHiddenTimer = setTimeout(check, 1000);
                }
            }, 1000);
        }
    });

    // 监听resize时打个临时标记，vchange时若标记为true则认定为是在最大化，不会触发自动PV和PL事件
    addEvent(window, 'resize', function() {
        isJustResized = true;

        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            isJustResized = false;
        }, 2000);
    });


    // 监听单页面路由事件
    addSinglePageEvent(function(lastUrl) {
        if (lastUrl !== location.href) {
            // 清除上一页监听的曝光元素
            exposurePlugin.clear();
        }
    });
};

import NetworkManager from './network';
import Logger from './utils/logger';
import global from './utils/global';
import { isFunc, isArray, isStr } from './utils/type';
import { spiderUA, getDeviceId, getPageTraceId, parseUA, generatTraceId, extend, getAndUpdateSeq, base64Encode, now, getSession, generateMSID } from './utils';
import { VERSION } from './constant/version';

// ext长度限制，默认长度7000
const EXT_LENGTH_LIMIT = 7000;

// 内存session
window.BlmAnalysisMS = window.BlmAnalysisMS || (`${generateMSID()}`);

// 内存seq（上报序号）
window.BlmAnalysisSeq = window.BlmAnalysisSeq || 0;

/**
 * 页面性能管理
 *
 * @class EventManager
 */
class EventManager {
    constructor(cfgManager) {
        // 实例化网络请求
        this.network = new NetworkManager(cfgManager);
        this.cfgManager = cfgManager;
        // PV上报次数，含自动上报PV
        this.pvNum = 0;
        // pl事件缓存队列
        this.plStack = {};
        // 上报数量
        this.reportCount = 0;
        // 当前上报的PV的参数
        this.currentPageOps = {};
        // 生成pageTraceId
        this.pageTraceId = cfgManager.get('pageTraceId');
    }
    /**
     * 缓存页面离开事件
     * @param {Object} opts 参数
     * @returns {Object}
     */
    addPageLeave(category, sendData) {
        this.plStack[category] = extend({}, sendData);
    }
    /**
     * 发送全部或指定业务线的页面离开事件
     * @param {Object} opts 参数
     * @returns {Object}
     */
    sendPageLeave(opts) {
        const nowTime = opts.timeStamp || now();

        for (let category in this.plStack) {
            const evs = this.plStack[category].evs[0];
            evs.type =  'pl';
            evs.duration =  nowTime - evs.ts;
            evs.ts =  nowTime;
            evs.seq = getAndUpdateSeq();
            evs.isAuto =  opts.isAuto || 1;
            evs.innerData = extend({ seq: ++ window.BlmAnalysisSeq }, evs.innerData )
            delete evs.firstPV;
            // 上报页面离开埋点
            this.network.send(this.plStack[category]);
            delete this.plStack[category];
        }
    }
    /**
     * 效验埋点参数
     * @param {object} opts
     */
    validationParams(opts = {}) {
        let isValidate = true;

        const { pageId, eventId } = opts;

        if (!eventId) {
            Logger.ignore('eventId不得为空');
            isValidate = false;
        }

        if (!isStr(pageId)) {
            Logger.ignore('传入的eventId必须是字符类型');
            isValidate = false;
        }

        if (!pageId) {
            Logger.ignore('pageId不得为空');
            isValidate = false;
        }

        if (!isStr(pageId)) {
            Logger.ignore('传入的pageId必须是字符类型');
            isValidate = false;
        }

        return isValidate; 
    }
    /**
     * 统一组装上报参数
     *
     * @returns {Object}
     */
    parse() {
        const { traceId, pageTraceId, devMode, appnm, category, tenantId, channel, envVar, uid, debug } = this.cfgManager.get();

        const { isIOS, isAndroid, isWindows, isMac } = parseUA();

        let pf = 0; // 操作系统，1：iOS、2：Android、3：Windows、4：MacOS

        if (isIOS) {
            pf = 1;
        } else if (isAndroid) {
            pf = 2;
        } else if (isWindows) {
            pf = 3;
        } else if (isMac) {
            pf = 4;
        } else {
            pf = 5;
        }

        // 屏幕分辨率
        let screen = window.screen.width + '*' + window.screen.height;

        const data = {
            nt: 0,                                          // 埋点上报方式 0：前端上报（PC、H5）, 1：natvie上报, 2：AJX上报，3：小程序上报
            screen,                                         // 屏幕分辨率
            traceId,                                        // 容器串联标识
            sdkVersion: VERSION,                        // sdk版本
            sdkEnv: devMode ? 'offline' : 'online',         // 上报环境，online：线上，offline：线下                                     // 业务扩展字段
            misid: getSession(0),                           // misid，有效时间30分钟
            statCommon: {
                appnm,                                      // 应用名
                category,                                   // 业务线
                diu: getDeviceId(),                         // 设备id
                ts: (new Date).getTime(),
                tenantId,                                   // 租户ID
                channel,
                uid,                                        // 用户id，用户可手动设置
                pf,                                         // 操作系统，1：iOS、2：Android、3：Windows、4：MacOS
                envVar                                      // 针对灰度切流、注入的环境变量
            }
        };

        // 埋点验证模式
        if (debug) {
            data.debug = debug;
        }
    
        return data;
    }
    /**
     * 上报报文evs组装
     * @param {Object} data 埋点信息
     */
    webEvsWrapper(data) {
        let { ext, type, isAuto = 0, pageId, eventId, firstPV } = data;

        // 上一页URL
        const referrer = document.referrer;
        const pageTraceId = this.pageTraceId || this.cfgManager.get('pageTraceId')

        let evs = {
            seq: getAndUpdateSeq(),
            pageId, // 页面唯一标识
            eventId, // 事件唯一标识
            pageTraceId,
            url: location.href,
            type,
            ext,
            ts: new Date().getTime(),
            innerData: {
                seq: ++ window.BlmAnalysisSeq,
                misid: window.BlmAnalysisMS
            }
        };

        // 是否是PV事件
        const isPVEvent = type === 'pv';

        // PV事件
        if (isPVEvent) {
            // 是否自动PV上报
            evs.isAuto = isAuto;
            // 存在上一页
            if (referrer && isPVEvent) {
                evs.referrer = referrer;
            }

            // 业务手动首次上报的PV
            evs.firstPV = firstPV || false;
        }

        if (ext) {
            let extLen = JSON.stringify(ext).length;

            if (extLen >= EXT_LENGTH_LIMIT) {
                Logger.ignore(`lab超过长度限制，已裁切。原ext：${ext}`);
                evs.ext = {
                    overLen: extLen
                }; 
            }
        }

        return evs;
    }
    /**
     * 报文组装
     * @param {Object} data 报文数据
     * @param {Array} evs 事件体
     */
    dataWrapper(evs) {
        if (!isArray(evs)) {
            evs = [evs];
        }
        
        const data = {
            ...this.parse(),
            evs
        }

        return data;
    }
    /**
     * 获取当前PV的参数
     * @param {Object} opts 参数
     */
    getCurrentPageOps() {
        const curPageUrl = global.getHref();
        const { currentPageOps } = this;
        if (!currentPageOps[curPageUrl]) {
            return null;
        }

        return currentPageOps[curPageUrl];
    }
    /**
     * 页面PV事件
     * @param {Object} opts 参数
     * @param {boolean} forceUpdate 是否强制更新pageTraceId
     */
    pageView(opts, forceUpdate) {
        if (!this.validationParams(opts)) {
            return;
        }

        // 发送上个页面未上报的PL事件
        this.sendPageLeave({
            isAuto: 1   // 0：业务手动上报，1：SDK自动上报
        });

        // 更新pageTraceId
        this.pageTraceId = getPageTraceId();
        const evs = this.webEvsWrapper(opts);
        const data = this.dataWrapper(evs);

        const { category } = this.cfgManager.get();

        // 缓存页面离开事件
        this.addPageLeave(category, data);

        const curPageUrl = global.getHref();
        // 存储当前PV的参数
        this.currentPageOps = {
            [curPageUrl] : opts
        };

        this.network.send(data);
    }
    /**
     * 页面离开事件
     * @param {Object} opts 参数
     */
    pageLeave(opts) {
        const { isAuto = 0, timeStamp } = opts;

        this.sendPageLeave({
            isAuto,   // 0：业务手动上报，1：SDK自动上报
            ts: timeStamp || '' // 上报时间
        });
    }
    /**
     * 模块点击事件
     * @param {Object} opts 参数
     */
    moduleClick(opts) {
        if (!this.validationParams(opts)) {
            return;
        }

        const evs = this.webEvsWrapper(opts);
        const data = this.dataWrapper(evs);

        this.network.send(data);
    }
    /**
     * 模块曝光事件
     * @param {Object} opts 参数
     */
    moduleExposure(opts) {
        if (!this.validationParams(opts)) {
            return;
        }

        const evs = this.webEvsWrapper(opts);
        const data = this.dataWrapper(evs);

        this.network.send(data);
    }
};

export default EventManager;

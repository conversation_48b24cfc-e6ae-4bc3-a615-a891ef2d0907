import global from './utils/global';
import { getCookie, setCookie } from './utils'
import { DEBUG_REPORT_COOKIE_NAME } from './constant'

const DEBUG_SEARCH_REGEX = /__blmValidation=([^&]*)(&|$)/i;
// 埋点验证有效时间，默认30分钟
const DEBUG_COOKIE_TIME = 30;


// 提前检测是否处于验证模式，用于向环境信息中写入用户账户名
export default function validateStateCheck(cfgManager) {
    // 获取URL查询字符串
    let search = ''
    try {
        search = decodeURIComponent(global.getSearch());
    } catch (error) {
        console.error('产品业务埋点SDK解码异常')
    }
    
    // 从URL获取__blmValidation参数上携带的用户账号名
    // 埋点验证模式才存在（用户在埋点测试平台上进行web埋点验证）
    const debugInfo = search.match(DEBUG_SEARCH_REGEX) || [];
    const account = getCookie(DEBUG_REPORT_COOKIE_NAME) || '';

    // 用户账户名
    let userAccount = debugInfo && debugInfo[1] || account || '';

    if (userAccount) {
        // 将用户账户名设置到当前域名的cookie里
        setCookie(DEBUG_REPORT_COOKIE_NAME, userAccount, DEBUG_COOKIE_TIME);
        // 将用户账户名设置放到全局配置上
        cfgManager.set({ debug: userAccount })
    }
};
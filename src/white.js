import { OFFLINE_URL } from './constant';
import <PERSON> from './xhr';
import { getDeviceId } from './utils';

// 是否已初始化
let isStarted = false;

// 检测当前用户是否在白名单
export default function validateUserWhite(cfgManager) {
    const { devMode } = cfgManager.get();

    // 当前是线下环境
    if (devMode) {
        const url = `https://eventlog-test.${window.mainDomain || 'yueyuechuxing.cn'}/admin/v1/eventlog/tracking/sdk/isExistWhite`;

        const { category, appnm, uid } = cfgManager.get();

        // 用户id不存在 || 业务线不存在 || 应用不存在，不走接下来的白名单检测
        if (!uid || !category || !appnm) {
            return;
        }

        // 只初始化一次
        if (isStarted) { 
            return;
        }

        isStarted = true;

        const data = {
            category,
            appnm,
            userId: uid,
            diu: getDeviceId()
        };


        Ajax({
            type: 'POST',
            url,
            header: {
                'Content-Type': 'application/json'
            },
            data: JSON.stringify(data),
            success: (res) => {
                // 命中白名单， tue：命中  false：未命中
                if (res.data) {
                    cfgManager.updateReportDomain(OFFLINE_URL)
                }
            }
        });
    }
};

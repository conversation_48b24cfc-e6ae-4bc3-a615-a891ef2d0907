// rollup.config.js
import fs from 'fs';
import resolve from 'rollup-plugin-node-resolve';
import commonjs from 'rollup-plugin-commonjs';
import { terser } from 'rollup-plugin-terser';
import babel from 'rollup-plugin-babel';
import serve from 'rollup-plugin-serve';
import builtins from 'rollup-plugin-node-builtins';
import replace from 'rollup-plugin-replace';
import minimist from 'minimist';
import http from 'http';

const NODE_ENV = process.env['NODE_ENV'];
const PORT = 8031;
const isDev = NODE_ENV === 'dev';
const argv = minimist(process.argv.slice(2));

if (isDev) {
    serve({
        host: '0.0.0.0',//不加这行其他机器无法通过ip访问本机。方便虚拟机调试。
        open: true,
        contentBase: ['demo'],
        port: PORT,
        headers: {
            'Access-Control-Expose-Headers': 'LX-Request-Config',
            'LX-Request-Config': 'techportal;b_orderid;b_bid;order;x=a&y=b',
            'Access-Control-Allow-Origin': '*',
            'Cache-Control': 'public, max-age=3600'
        }
    });

    let retryTime = 0;
    let key = setInterval(function () {
        retryTime++;
        if (retryTime > 10) {
            clearInterval(key);
            console.log('启动失败');
            return;
        }
        http.get(`http://127.0.0.1:${PORT}/`, function (res) {
            if (res.statusCode === 200) {
                clearInterval(key);
                setTimeout(function () {
                    console.log(`启动成功，请访问：http://127.0.0.1:${PORT}/`);
                }, 2000);
            }
        });
    }, 1000);
}

const buildFormats = [];

const baseConfig = {
    input: 'src/index.js',
    plugins: {
        babel: {
            exclude: 'node_modules'
        },
    }
};

if (!argv.format || argv.format === 'es') {
    const esConfig = {
        ...baseConfig,
        output: {
            file: 'dist/blm-analysis.esm.js',
            format: 'esm'
        },
        plugins: [
            resolve(),
            commonjs(),
            replace({
                'process.env.NODE_ENV': JSON.stringify('production')
            }),
            babel({
                ...baseConfig.plugins.babel,
                presets: [
                    [
                        '@babel/preset-env'
                    ],
                ],
            }),
        ],
    };
    buildFormats.push(esConfig);
}

if (!argv.format || argv.format === 'cjs') {
    const umdConfig = {
        ...baseConfig,
        output: {
            compact: true,
            file: 'dist/blm-analysis.ssr.js',
            format: 'cjs',
            name: 'BlmAnalysis'
        },
        plugins: [
            resolve(),
            commonjs(),
            replace({
                'process.env.NODE_ENV': JSON.stringify('production')
            }),
            babel({
                ...baseConfig.plugins.babel,
                presets: [
                    [
                        '@babel/preset-env'
                    ],
                ],
            }),
        ],
    };
    buildFormats.push(umdConfig);
}

if (!argv.format || argv.format === 'iife') {
    const unpkgConfig = {
        ...baseConfig,
        output: {
            compact: true,
            file: 'dist/blm-analysis.min.js',
            format: 'iife',
            name: 'BlmMonitor'
        },
        plugins: [
            replace({
                'process.env.NODE_ENV': JSON.stringify('production')
            }),
            babel({
                ...baseConfig.plugins.babel,
                presets: [
                    [
                        '@babel/preset-env'
                    ],
                ],
            }),
            resolve(),
            commonjs(),
            terser({
                output: {
                    ecma: 5,
                },
            }),
        ],
    };
    buildFormats.push(unpkgConfig);
}

if (!argv.format || argv.format === 'umd') {
    const unpkgConfig = {
        ...baseConfig,
        output: {
            compact: true,
            file: 'dist/blm-analysis.umd.min.js',
            format: 'umd',
            name: 'BlmAnalysis'
        },
        plugins: [
            replace({
                'process.env.NODE_ENV': JSON.stringify('production')
            }),
            babel({
                ...baseConfig.plugins.babel,
                presets: [
                    [
                        '@babel/preset-env'
                    ],
                ],
            }),
            resolve(),
            commonjs(),
            terser({
                output: {
                    ecma: 5,
                },
            }),
        ],
    };
    buildFormats.push(unpkgConfig);
}

export default buildFormats;

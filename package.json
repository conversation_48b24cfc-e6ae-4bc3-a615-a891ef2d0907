{"name": "blm-analysis", "version": "1.0.16", "description": "", "main": "dist/blm-analysis.ssr.js", "browser": "dist/blm-analysis.esm.js", "module": "dist/blm-analysis.esm.js", "unpkg": "dist/blm-analysis.umd.min.js", "files": ["dist/*"], "scripts": {"dev": "cross-env NODE_ENV=dev && rollup --config build/rollup.config.js", "build": "cross-env NODE_ENV=production rollup --config build/rollup.config.js", "watch": "cross-env NODE_ENV=production rollup --config build/rollup.config.js -w --sourcemap", "build:ssr": "cross-env NODE_ENV=production rollup --config build/rollup.config.js --format cjs", "build:es": "cross-env NODE_ENV=production rollup --config build/rollup.config.js --format es", "build:unpkg": "cross-env NODE_ENV=production rollup --config build/rollup.config.js --format iife", "build:umd": "cross-env NODE_ENV=production rollup --config build/rollup.config.js --format umd"}, "author": "wa<PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"@babel/core": "^7.22.1", "@babel/preset-env": "^7.22.4", "babel-core": "^6.26.3", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.1", "cross-env": "^6.0.3", "jest": "^29.5.0", "minimist": "^1.2.8", "rollup": "^2", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-builtins": "^2.1.2", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-serve": "^1.0.1", "rollup-plugin-terser": "^7.0.2"}, "dependencies": {"jsencrypt": "3.3.2"}}